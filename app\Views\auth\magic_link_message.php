<?= $this->extend('auth/layout') ?>

<?= $this->section('title') ?>Reset Link Sent<?= $this->endSection() ?>

<?= $this->section('main') ?>
<div class="space-y-6 text-center">
    <!-- Icon -->
    <div class="flex justify-center">
        <div class="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center">
            <i class="fas fa-check-circle text-3xl text-green-600"></i>
        </div>
    </div>

    <!-- Header -->
    <div>
        <h2 class="text-2xl font-bold text-gray-900 mb-2">Reset Link Sent!</h2>
        <p class="text-gray-600">Check your email for the password reset link</p>
    </div>

    <!-- Message -->
    <div class="bg-green-50 border border-green-200 rounded-lg p-4">
        <div class="flex items-start">
            <i class="fas fa-envelope text-green-500 mt-1 mr-3"></i>
            <div class="text-left">
                <h3 class="text-sm font-medium text-green-800 mb-1">Email <PERSON> Successfully</h3>
                <p class="text-sm text-green-700">
                    We've sent a password reset link to your email address. 
                    Click the link in the email to reset your password.
                </p>
            </div>
        </div>
    </div>

    <!-- Instructions -->
    <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
        <h3 class="text-sm font-medium text-gray-800 mb-2">Next Steps</h3>
        <ol class="text-sm text-gray-600 space-y-1 text-left">
            <li>1. Check your email inbox</li>
            <li>2. Look for an email from Student Management System</li>
            <li>3. Click the "Reset Password" link in the email</li>
            <li>4. Create a new password</li>
            <li>5. Sign in with your new password</li>
        </ol>
    </div>

    <!-- Resend Option -->
    <div class="space-y-4">
        <p class="text-gray-600">Didn't receive the email?</p>
        
        <a 
            href="<?= route_to('magic-link') ?>" 
            class="inline-flex items-center justify-center px-6 py-3 border border-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-50 transition-all duration-200"
        >
            <i class="fas fa-redo mr-2"></i>
            Send Another Link
        </a>
    </div>

    <!-- Help -->
    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div class="flex items-start">
            <i class="fas fa-exclamation-triangle text-yellow-500 mt-1 mr-3"></i>
            <div class="text-left">
                <h3 class="text-sm font-medium text-yellow-800 mb-1">Troubleshooting</h3>
                <ul class="text-sm text-yellow-700 space-y-1">
                    <li>• Check your spam/junk folder</li>
                    <li>• Make sure you entered the correct email address</li>
                    <li>• The link expires in 1 hour</li>
                    <li>• Contact support if you need assistance</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Back to Login -->
    <div class="pt-4">
        <a 
            href="<?= route_to('login') ?>" 
            class="text-primary-600 hover:text-primary-500 font-medium transition-colors duration-200 flex items-center justify-center"
        >
            <i class="fas fa-arrow-left mr-2"></i>
            Back to Login
        </a>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
// Auto-redirect to login after 5 minutes
setTimeout(function() {
    Swal.fire({
        icon: 'info',
        title: 'Redirecting...',
        text: 'Redirecting to login page for security.',
        timer: 3000,
        showConfirmButton: false
    }).then(() => {
        window.location.href = '<?= route_to('login') ?>';
    });
}, 300000); // 5 minutes
</script>
<?= $this->endSection() ?>
