<?= $this->extend('admin/layout') ?>

<?= $this->section('content') ?>

<!-- Breadcrumb -->
<div class="mb-6 flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
    <h2 class="text-title-md2 font-semibold text-black dark:text-white">
        Student Apps
    </h2>

    <nav>
        <ol class="flex items-center gap-2">
            <?php if (!empty($breadcrumbs)): ?>
                <?php foreach ($breadcrumbs as $breadcrumb): ?>
                    <li>
                        <?php if (!empty($breadcrumb['url'])): ?>
                            <a class="font-medium" href="<?= $breadcrumb['url'] ?>"><?= $breadcrumb['name'] ?> /</a>
                        <?php else: ?>
                            <span class="font-medium text-primary"><?= $breadcrumb['name'] ?></span>
                        <?php endif; ?>
                    </li>
                <?php endforeach; ?>
            <?php else: ?>
                <li>
                    <a class="font-medium" href="<?= base_url('admin') ?>">Dashboard /</a>
                </li>
                <li class="font-medium text-primary">Student Apps</li>
            <?php endif; ?>
        </ol>
    </nav>
</div>

<!-- Overview Stats -->
<div class="grid grid-cols-1 gap-4 md:grid-cols-2 md:gap-6 xl:grid-cols-4 2xl:gap-7.5 mb-6">
    <!-- Total Students -->
    <div class="rounded-sm border border-stroke bg-white py-6 px-7.5 shadow-default dark:border-strokedark dark:bg-boxdark">
        <div class="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-meta-2 dark:bg-meta-4">
            <i class="fas fa-users text-primary text-xl"></i>
        </div>
        <div class="mt-4 flex items-end justify-between">
            <div>
                <h4 class="text-title-md font-bold text-black dark:text-white" id="total-students">
                    0
                </h4>
                <span class="text-sm font-medium">Total Students</span>
            </div>
        </div>
    </div>

    <!-- Active Sessions -->
    <div class="rounded-sm border border-stroke bg-white py-6 px-7.5 shadow-default dark:border-strokedark dark:bg-boxdark">
        <div class="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-meta-2 dark:bg-meta-4">
            <i class="fas fa-calendar-alt text-primary text-xl"></i>
        </div>
        <div class="mt-4 flex items-end justify-between">
            <div>
                <h4 class="text-title-md font-bold text-black dark:text-white" id="active-sessions">
                    0
                </h4>
                <span class="text-sm font-medium">Active Sessions</span>
            </div>
        </div>
    </div>

    <!-- Total Applications -->
    <div class="rounded-sm border border-stroke bg-white py-6 px-7.5 shadow-default dark:border-strokedark dark:bg-boxdark">
        <div class="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-meta-2 dark:bg-meta-4">
            <i class="fas fa-file-alt text-primary text-xl"></i>
        </div>
        <div class="mt-4 flex items-end justify-between">
            <div>
                <h4 class="text-title-md font-bold text-black dark:text-white" id="total-applications">
                    0
                </h4>
                <span class="text-sm font-medium">Applications</span>
            </div>
        </div>
    </div>

    <!-- Pending Approvals -->
    <div class="rounded-sm border border-stroke bg-white py-6 px-7.5 shadow-default dark:border-strokedark dark:bg-boxdark">
        <div class="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-meta-2 dark:bg-meta-4">
            <i class="fas fa-clock text-warning text-xl"></i>
        </div>
        <div class="mt-4 flex items-end justify-between">
            <div>
                <h4 class="text-title-md font-bold text-black dark:text-white" id="pending-approvals">
                    0
                </h4>
                <span class="text-sm font-medium">Pending Approvals</span>
            </div>
        </div>
    </div>
</div>

<!-- Student Apps Modules -->
<div class="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5">
    <!-- Apply Leave -->
    <div class="group rounded-lg border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark hover:shadow-xl hover:border-primary/20 transition-all duration-300 transform hover:-translate-y-1">
        <div class="p-6">
            <div class="flex items-center justify-center w-16 h-16 mx-auto mb-4 rounded-full bg-blue-100 dark:bg-blue-900 group-hover:bg-blue-200 dark:group-hover:bg-blue-800 transition-colors duration-300">
                <i class="fas fa-calendar-times text-blue-600 dark:text-blue-300 text-2xl group-hover:scale-110 transition-transform duration-300"></i>
            </div>
            <h3 class="text-lg font-semibold text-center text-black dark:text-white mb-2 group-hover:text-primary transition-colors duration-300">Apply Leave</h3>
            <p class="text-sm text-gray-600 dark:text-gray-400 text-center mb-4">Manage student leave applications and approvals</p>
            <a href="<?= base_url('admin/student-apps/apply-leave') ?>" class="block w-full text-center bg-gradient-to-r from-primary to-primary/80 text-white py-3 px-4 rounded-lg hover:from-primary/90 hover:to-primary/70 transition-all duration-300 font-medium shadow-lg hover:shadow-xl">
                <i class="fas fa-arrow-right mr-2"></i>Manage
            </a>
        </div>
    </div>

    <!-- Attendance -->
    <div class="group rounded-lg border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark hover:shadow-xl hover:border-primary/20 transition-all duration-300 transform hover:-translate-y-1">
        <div class="p-6">
            <div class="flex items-center justify-center w-16 h-16 mx-auto mb-4 rounded-full bg-green-100 dark:bg-green-900 group-hover:bg-green-200 dark:group-hover:bg-green-800 transition-colors duration-300">
                <i class="fas fa-user-check text-green-600 dark:text-green-300 text-2xl group-hover:scale-110 transition-transform duration-300"></i>
            </div>
            <h3 class="text-lg font-semibold text-center text-black dark:text-white mb-2 group-hover:text-primary transition-colors duration-300">Attendance</h3>
            <p class="text-sm text-gray-600 dark:text-gray-400 text-center mb-4">Track and manage student daily attendance</p>
            <a href="<?= base_url('admin/student-apps/attendance') ?>" class="block w-full text-center bg-gradient-to-r from-green-500 to-green-600 text-white py-3 px-4 rounded-lg hover:from-green-600 hover:to-green-700 transition-all duration-300 font-medium shadow-lg hover:shadow-xl">
                <i class="fas fa-arrow-right mr-2"></i>Manage
            </a>
        </div>
    </div>

    <!-- Behaviour -->
    <div class="group rounded-lg border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark hover:shadow-xl hover:border-primary/20 transition-all duration-300 transform hover:-translate-y-1">
        <div class="p-6">
            <div class="flex items-center justify-center w-16 h-16 mx-auto mb-4 rounded-full bg-yellow-100 dark:bg-yellow-900 group-hover:bg-yellow-200 dark:group-hover:bg-yellow-800 transition-colors duration-300">
                <i class="fas fa-star text-yellow-600 dark:text-yellow-300 text-2xl group-hover:scale-110 transition-transform duration-300"></i>
            </div>
            <h3 class="text-lg font-semibold text-center text-black dark:text-white mb-2 group-hover:text-primary transition-colors duration-300">Behaviour</h3>
            <p class="text-sm text-gray-600 dark:text-gray-400 text-center mb-4">Monitor and record student behaviour patterns</p>
            <a href="<?= base_url('admin/student-apps/behaviour') ?>" class="block w-full text-center bg-gradient-to-r from-yellow-500 to-yellow-600 text-white py-3 px-4 rounded-lg hover:from-yellow-600 hover:to-yellow-700 transition-all duration-300 font-medium shadow-lg hover:shadow-xl">
                <i class="fas fa-arrow-right mr-2"></i>Manage
            </a>
        </div>
    </div>

    <!-- Documents -->
    <div class="group rounded-lg border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark hover:shadow-xl hover:border-primary/20 transition-all duration-300 transform hover:-translate-y-1">
        <div class="p-6">
            <div class="flex items-center justify-center w-16 h-16 mx-auto mb-4 rounded-full bg-purple-100 dark:bg-purple-900 group-hover:bg-purple-200 dark:group-hover:bg-purple-800 transition-colors duration-300">
                <i class="fas fa-folder-open text-purple-600 dark:text-purple-300 text-2xl group-hover:scale-110 transition-transform duration-300"></i>
            </div>
            <h3 class="text-lg font-semibold text-center text-black dark:text-white mb-2 group-hover:text-primary transition-colors duration-300">Documents</h3>
            <p class="text-sm text-gray-600 dark:text-gray-400 text-center mb-4">Manage student documents and files</p>
            <a href="<?= base_url('admin/student-apps/documents') ?>" class="block w-full text-center bg-gradient-to-r from-purple-500 to-purple-600 text-white py-3 px-4 rounded-lg hover:from-purple-600 hover:to-purple-700 transition-all duration-300 font-medium shadow-lg hover:shadow-xl">
                <i class="fas fa-arrow-right mr-2"></i>Manage
            </a>
        </div>
    </div>

    <!-- Fees Management -->
    <div class="group rounded-lg border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark hover:shadow-xl hover:border-primary/20 transition-all duration-300 transform hover:-translate-y-1">
        <div class="p-6">
            <div class="flex items-center justify-center w-16 h-16 mx-auto mb-4 rounded-full bg-red-100 dark:bg-red-900 group-hover:bg-red-200 dark:group-hover:bg-red-800 transition-colors duration-300">
                <i class="fas fa-dollar-sign text-red-600 dark:text-red-300 text-2xl group-hover:scale-110 transition-transform duration-300"></i>
            </div>
            <h3 class="text-lg font-semibold text-center text-black dark:text-white mb-2 group-hover:text-primary transition-colors duration-300">Fees</h3>
            <p class="text-sm text-gray-600 dark:text-gray-400 text-center mb-4">Complete fees management system</p>
            <a href="<?= base_url('admin/student-apps/fees') ?>" class="block w-full text-center bg-gradient-to-r from-red-500 to-red-600 text-white py-3 px-4 rounded-lg hover:from-red-600 hover:to-red-700 transition-all duration-300 font-medium shadow-lg hover:shadow-xl">
                <i class="fas fa-arrow-right mr-2"></i>Manage
            </a>
        </div>
    </div>

    <!-- Incidents -->
    <div class="group rounded-lg border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark hover:shadow-xl hover:border-primary/20 transition-all duration-300 transform hover:-translate-y-1">
        <div class="p-6">
            <div class="flex items-center justify-center w-16 h-16 mx-auto mb-4 rounded-full bg-orange-100 dark:bg-orange-900 group-hover:bg-orange-200 dark:group-hover:bg-orange-800 transition-colors duration-300">
                <i class="fas fa-exclamation-triangle text-orange-600 dark:text-orange-300 text-2xl group-hover:scale-110 transition-transform duration-300"></i>
            </div>
            <h3 class="text-lg font-semibold text-center text-black dark:text-white mb-2 group-hover:text-primary transition-colors duration-300">Incidents</h3>
            <p class="text-sm text-gray-600 dark:text-gray-400 text-center mb-4">Track and manage student incidents</p>
            <a href="<?= base_url('admin/student-apps/incidents') ?>" class="block w-full text-center bg-gradient-to-r from-orange-500 to-orange-600 text-white py-3 px-4 rounded-lg hover:from-orange-600 hover:to-orange-700 transition-all duration-300 font-medium shadow-lg hover:shadow-xl">
                <i class="fas fa-arrow-right mr-2"></i>Manage
            </a>
        </div>
    </div>

    <!-- Sessions -->
    <div class="group rounded-lg border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark hover:shadow-xl hover:border-primary/20 transition-all duration-300 transform hover:-translate-y-1">
        <div class="p-6">
            <div class="flex items-center justify-center w-16 h-16 mx-auto mb-4 rounded-full bg-indigo-100 dark:bg-indigo-900 group-hover:bg-indigo-200 dark:group-hover:bg-indigo-800 transition-colors duration-300">
                <i class="fas fa-graduation-cap text-indigo-600 dark:text-indigo-300 text-2xl group-hover:scale-110 transition-transform duration-300"></i>
            </div>
            <h3 class="text-lg font-semibold text-center text-black dark:text-white mb-2 group-hover:text-primary transition-colors duration-300">Sessions</h3>
            <p class="text-sm text-gray-600 dark:text-gray-400 text-center mb-4">Manage student session enrollments</p>
            <a href="<?= base_url('admin/student-apps/sessions') ?>" class="block w-full text-center bg-gradient-to-r from-indigo-500 to-indigo-600 text-white py-3 px-4 rounded-lg hover:from-indigo-600 hover:to-indigo-700 transition-all duration-300 font-medium shadow-lg hover:shadow-xl">
                <i class="fas fa-arrow-right mr-2"></i>Manage
            </a>
        </div>
    </div>

    <!-- Timeline -->
    <div class="group rounded-lg border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark hover:shadow-xl hover:border-primary/20 transition-all duration-300 transform hover:-translate-y-1">
        <div class="p-6">
            <div class="flex items-center justify-center w-16 h-16 mx-auto mb-4 rounded-full bg-teal-100 dark:bg-teal-900 group-hover:bg-teal-200 dark:group-hover:bg-teal-800 transition-colors duration-300">
                <i class="fas fa-timeline text-teal-600 dark:text-teal-300 text-2xl group-hover:scale-110 transition-transform duration-300"></i>
            </div>
            <h3 class="text-lg font-semibold text-center text-black dark:text-white mb-2 group-hover:text-primary transition-colors duration-300">Timeline</h3>
            <p class="text-sm text-gray-600 dark:text-gray-400 text-center mb-4">Track student academic timeline</p>
            <a href="<?= base_url('admin/student-apps/timeline') ?>" class="block w-full text-center bg-gradient-to-r from-teal-500 to-teal-600 text-white py-3 px-4 rounded-lg hover:from-teal-600 hover:to-teal-700 transition-all duration-300 font-medium shadow-lg hover:shadow-xl">
                <i class="fas fa-arrow-right mr-2"></i>Manage
            </a>
        </div>
    </div>

    <!-- Subject Attendance -->
    <div class="group rounded-lg border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark hover:shadow-xl hover:border-primary/20 transition-all duration-300 transform hover:-translate-y-1">
        <div class="p-6">
            <div class="flex items-center justify-center w-16 h-16 mx-auto mb-4 rounded-full bg-cyan-100 dark:bg-cyan-900 group-hover:bg-cyan-200 dark:group-hover:bg-cyan-800 transition-colors duration-300">
                <i class="fas fa-chalkboard-teacher text-cyan-600 dark:text-cyan-300 text-2xl group-hover:scale-110 transition-transform duration-300"></i>
            </div>
            <h3 class="text-lg font-semibold text-center text-black dark:text-white mb-2 group-hover:text-primary transition-colors duration-300">Subject Attendance</h3>
            <p class="text-sm text-gray-600 dark:text-gray-400 text-center mb-4">Track attendance by subject and class</p>
            <a href="<?= base_url('admin/student-apps/subject-attendance') ?>" class="block w-full text-center bg-gradient-to-r from-cyan-500 to-cyan-600 text-white py-3 px-4 rounded-lg hover:from-cyan-600 hover:to-cyan-700 transition-all duration-300 font-medium shadow-lg hover:shadow-xl">
                <i class="fas fa-arrow-right mr-2"></i>Manage
            </a>
        </div>
    </div>

    <!-- Transport Fees -->
    <div class="group rounded-lg border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark hover:shadow-xl hover:border-primary/20 transition-all duration-300 transform hover:-translate-y-1">
        <div class="p-6">
            <div class="flex items-center justify-center w-16 h-16 mx-auto mb-4 rounded-full bg-emerald-100 dark:bg-emerald-900 group-hover:bg-emerald-200 dark:group-hover:bg-emerald-800 transition-colors duration-300">
                <i class="fas fa-bus text-emerald-600 dark:text-emerald-300 text-2xl group-hover:scale-110 transition-transform duration-300"></i>
            </div>
            <h3 class="text-lg font-semibold text-center text-black dark:text-white mb-2 group-hover:text-primary transition-colors duration-300">Transport Fees</h3>
            <p class="text-sm text-gray-600 dark:text-gray-400 text-center mb-4">Manage student transport fees and routes</p>
            <a href="<?= base_url('admin/student-apps/transport-fees') ?>" class="block w-full text-center bg-gradient-to-r from-emerald-500 to-emerald-600 text-white py-3 px-4 rounded-lg hover:from-emerald-600 hover:to-emerald-700 transition-all duration-300 font-medium shadow-lg hover:shadow-xl">
                <i class="fas fa-arrow-right mr-2"></i>Manage
            </a>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Load statistics with animation
    loadStats();

    // Add loading animation to cards
    $('.group').each(function(index) {
        $(this).css('animation-delay', (index * 100) + 'ms');
        $(this).addClass('animate-fade-in-up');
    });

    function loadStats() {
        // Show loading state
        $('.text-title-md').each(function() {
            $(this).html('<i class="fas fa-spinner fa-spin"></i>');
        });

        $.get('<?= base_url('admin/student-apps/get-stats') ?>')
            .done(function(response) {
                if (response.success) {
                    // Animate numbers counting up
                    animateNumber('#total-students', response.data.total_students);
                    animateNumber('#active-sessions', response.data.active_sessions);
                    animateNumber('#total-applications', response.data.total_applications);
                    animateNumber('#pending-approvals', response.data.pending_approvals);
                } else {
                    showError('Failed to load statistics');
                }
            })
            .fail(function() {
                showError('Failed to load statistics');
                // Set default values
                $('#total-students').text('0');
                $('#active-sessions').text('0');
                $('#total-applications').text('0');
                $('#pending-approvals').text('0');
            });
    }

    function animateNumber(selector, finalValue) {
        const element = $(selector);
        const startValue = 0;
        const duration = 1000;
        const startTime = Date.now();

        function updateNumber() {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);
            const currentValue = Math.floor(startValue + (finalValue - startValue) * progress);

            element.text(currentValue.toLocaleString());

            if (progress < 1) {
                requestAnimationFrame(updateNumber);
            }
        }

        updateNumber();
    }

    function showError(message) {
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: message,
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000
            });
        } else {
            console.error(message);
        }
    }

    // Add hover effects for better UX
    $('.group').hover(
        function() {
            $(this).find('.fas').addClass('animate-pulse');
        },
        function() {
            $(this).find('.fas').removeClass('animate-pulse');
        }
    );
});
</script>

<style>
@keyframes fade-in-up {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in-up {
    animation: fade-in-up 0.6s ease-out forwards;
}

/* Custom scrollbar for better UX */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Loading animation */
.fa-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>
<?= $this->endSection() ?>
