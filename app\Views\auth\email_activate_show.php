<?= $this->extend('auth/layout') ?>

<?= $this->section('title') ?>Activate Account<?= $this->endSection() ?>

<?= $this->section('main') ?>
<div class="space-y-6 text-center">
    <!-- Icon -->
    <div class="flex justify-center">
        <div class="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center">
            <i class="fas fa-envelope-open-text text-3xl text-blue-600"></i>
        </div>
    </div>

    <!-- Header -->
    <div>
        <h2 class="text-2xl font-bold text-gray-900 mb-2">Check Your Email</h2>
        <p class="text-gray-600">We've sent an activation link to your email address</p>
    </div>

    <!-- Message -->
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div class="flex items-start">
            <i class="fas fa-info-circle text-blue-500 mt-1 mr-3"></i>
            <div class="text-left">
                <h3 class="text-sm font-medium text-blue-800 mb-1">Activation Required</h3>
                <p class="text-sm text-blue-700">
                    Please check your email and click the activation link to complete your registration. 
                    The link will expire in 24 hours.
                </p>
            </div>
        </div>
    </div>

    <!-- Resend Form -->
    <div class="space-y-4">
        <p class="text-gray-600">Didn't receive the email?</p>
        
        <?= form_open(route_to('auth-action-verify'), ['class' => 'space-y-4']) ?>
            <input type="hidden" name="token" value="<?= $token ?>">
            
            <button 
                type="submit" 
                class="btn-gradient text-white py-3 px-6 rounded-lg font-medium hover:shadow-lg transition-all duration-200 flex items-center justify-center mx-auto"
            >
                <i class="fas fa-paper-plane mr-2"></i>
                Resend Activation Email
            </button>
        <?= form_close() ?>
    </div>

    <!-- Help -->
    <div class="bg-gray-50 border border-gray-200 rounded-lg p-4">
        <h3 class="text-sm font-medium text-gray-800 mb-2">Need Help?</h3>
        <ul class="text-sm text-gray-600 space-y-1">
            <li>• Check your spam/junk folder</li>
            <li>• Make sure you entered the correct email address</li>
            <li>• Contact support if you continue having issues</li>
        </ul>
    </div>

    <!-- Back to Login -->
    <div class="pt-4">
        <a 
            href="<?= route_to('login') ?>" 
            class="text-primary-600 hover:text-primary-500 font-medium transition-colors duration-200 flex items-center justify-center"
        >
            <i class="fas fa-arrow-left mr-2"></i>
            Back to Login
        </a>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
// Auto-refresh page every 30 seconds to check for activation
let refreshInterval;

document.addEventListener('DOMContentLoaded', function() {
    // Check if user has been activated every 30 seconds
    refreshInterval = setInterval(function() {
        fetch('<?= base_url('auth/check-activation') ?>')
            .then(response => response.json())
            .then(data => {
                if (data.activated) {
                    clearInterval(refreshInterval);
                    Swal.fire({
                        icon: 'success',
                        title: 'Account Activated!',
                        text: 'Your account has been successfully activated. Redirecting to login...',
                        timer: 3000,
                        showConfirmButton: false
                    }).then(() => {
                        window.location.href = '<?= route_to('login') ?>';
                    });
                }
            })
            .catch(error => {
                console.log('Activation check failed:', error);
            });
    }, 30000);
});

// Clear interval when page is unloaded
window.addEventListener('beforeunload', function() {
    if (refreshInterval) {
        clearInterval(refreshInterval);
    }
});
</script>
<?= $this->endSection() ?>
