<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use CodeIgniter\Shield\Models\UserModel;
use App\Models\NotificationModel;

class TestAuthSystem extends BaseCommand
{
    protected $group       = 'Authentication';
    protected $name        = 'test:auth-system';
    protected $description = 'Test the complete authentication system including RBAC, OAuth, and notifications';

    public function run(array $params)
    {
        CLI::write('Testing Complete Authentication System...', 'yellow');
        CLI::newLine();

        $this->testDatabaseTables();
        $this->testAuthConfiguration();
        $this->testRBACConfiguration();
        $this->testAuthViews();
        $this->testOAuthConfiguration();
        $this->testNotificationSystem();
        $this->testUserCreation();

        CLI::newLine();
        CLI::write('✅ Authentication system test completed!', 'green');
    }

    private function testDatabaseTables()
    {
        CLI::write('1. Testing Database Tables...', 'yellow');
        
        $db = \Config\Database::connect();
        
        $requiredTables = [
            'users' => 'Shield Users Table',
            'auth_identities' => 'Shield Identities Table',
            'auth_logins' => 'Shield Login Attempts Table',
            'auth_token_logins' => 'Shield Token Logins Table',
            'auth_remember_tokens' => 'Shield Remember Tokens Table',
            'auth_groups_users' => 'Shield Groups Users Table',
            'auth_permissions_users' => 'Shield Permissions Users Table',
            'notifications' => 'Custom Notifications Table'
        ];

        foreach ($requiredTables as $table => $description) {
            if ($db->tableExists($table)) {
                CLI::write("   ✅ {$description} exists", 'green');
            } else {
                CLI::write("   ❌ {$description} missing", 'red');
            }
        }
    }

    private function testAuthConfiguration()
    {
        CLI::write('2. Testing Auth Configuration...', 'yellow');
        
        $authConfig = config('Auth');
        
        // Test redirects
        $redirects = $authConfig->redirects;
        if ($redirects['login'] === '/admin') {
            CLI::write('   ✅ Login redirect configured correctly', 'green');
        } else {
            CLI::write('   ❌ Login redirect not configured', 'red');
        }

        // Test views
        $views = $authConfig->views;
        if ($views['login'] === 'auth/login') {
            CLI::write('   ✅ Custom auth views configured', 'green');
        } else {
            CLI::write('   ❌ Custom auth views not configured', 'red');
        }

        // Test email activation
        $actions = $authConfig->actions;
        if ($actions['register'] !== null) {
            CLI::write('   ✅ Email activation enabled', 'green');
        } else {
            CLI::write('   ⚠️  Email activation disabled', 'yellow');
        }
    }

    private function testRBACConfiguration()
    {
        CLI::write('3. Testing RBAC Configuration...', 'yellow');
        
        $authGroups = config('AuthGroups');
        
        // Test groups
        $requiredGroups = ['superadmin', 'admin', 'principal', 'teacher', 'staff', 'parent', 'student'];
        $configuredGroups = array_keys($authGroups->groups);
        
        foreach ($requiredGroups as $group) {
            if (in_array($group, $configuredGroups)) {
                CLI::write("   ✅ Group '{$group}' configured", 'green');
            } else {
                CLI::write("   ❌ Group '{$group}' missing", 'red');
            }
        }

        // Test permissions
        $requiredPermissions = [
            'admin.access', 'students.view', 'student-apps.view', 
            'attendance.view', 'grades.view', 'reports.view'
        ];
        $configuredPermissions = array_keys($authGroups->permissions);
        
        foreach ($requiredPermissions as $permission) {
            if (in_array($permission, $configuredPermissions)) {
                CLI::write("   ✅ Permission '{$permission}' configured", 'green');
            } else {
                CLI::write("   ❌ Permission '{$permission}' missing", 'red');
            }
        }

        // Test default group
        if ($authGroups->defaultGroup === 'teacher') {
            CLI::write('   ✅ Default group set to teacher', 'green');
        } else {
            CLI::write('   ⚠️  Default group is: ' . $authGroups->defaultGroup, 'yellow');
        }
    }

    private function testAuthViews()
    {
        CLI::write('4. Testing Auth Views...', 'yellow');
        
        $requiredViews = [
            'auth/layout.php' => 'Auth Layout',
            'auth/login.php' => 'Login View',
            'auth/register.php' => 'Register View',
            'auth/email_activate_show.php' => 'Email Activation View',
            'auth/magic_link_form.php' => 'Password Reset Form',
            'auth/magic_link_message.php' => 'Password Reset Message',
            'auth/email/email_activate_email.php' => 'Activation Email Template',
            'auth/email/magic_link_email.php' => 'Password Reset Email Template'
        ];

        foreach ($requiredViews as $view => $description) {
            $viewPath = APPPATH . 'Views/' . $view;
            if (file_exists($viewPath)) {
                CLI::write("   ✅ {$description} exists", 'green');
            } else {
                CLI::write("   ❌ {$description} missing", 'red');
            }
        }
    }

    private function testOAuthConfiguration()
    {
        CLI::write('5. Testing OAuth Configuration...', 'yellow');
        
        // Test OAuth config file
        $oauthConfigPath = APPPATH . 'Config/OAuth.php';
        if (file_exists($oauthConfigPath)) {
            CLI::write('   ✅ OAuth configuration file exists', 'green');
        } else {
            CLI::write('   ❌ OAuth configuration file missing', 'red');
            return;
        }

        // Test Google OAuth controller
        $googleControllerPath = APPPATH . 'Controllers/GoogleAuthController.php';
        if (file_exists($googleControllerPath)) {
            CLI::write('   ✅ Google OAuth controller exists', 'green');
        } else {
            CLI::write('   ❌ Google OAuth controller missing', 'red');
        }

        // Test environment variables
        $clientId = env('GOOGLE_CLIENT_ID');
        $clientSecret = env('GOOGLE_CLIENT_SECRET');
        
        if (!empty($clientId) && !empty($clientSecret)) {
            CLI::write('   ✅ Google OAuth credentials configured', 'green');
        } else {
            CLI::write('   ⚠️  Google OAuth credentials not configured in .env', 'yellow');
        }
    }

    private function testNotificationSystem()
    {
        CLI::write('6. Testing Notification System...', 'yellow');
        
        // Test notification model
        $notificationModelPath = APPPATH . 'Models/NotificationModel.php';
        if (file_exists($notificationModelPath)) {
            CLI::write('   ✅ Notification model exists', 'green');
        } else {
            CLI::write('   ❌ Notification model missing', 'red');
            return;
        }

        // Test notification functionality
        try {
            $notificationModel = new NotificationModel();
            
            // Test if we can create a test notification (if users exist)
            $userModel = new UserModel();
            $testUser = $userModel->first();
            
            if ($testUser) {
                $result = $notificationModel->createAuthNotification($testUser->id, 'register');
                if ($result) {
                    CLI::write('   ✅ Notification creation works', 'green');
                    
                    // Clean up test notification
                    $notificationModel->where('user_id', $testUser->id)
                                     ->where('type', 'success')
                                     ->delete();
                } else {
                    CLI::write('   ❌ Notification creation failed', 'red');
                }
            } else {
                CLI::write('   ⚠️  No users found to test notifications', 'yellow');
            }
        } catch (\Exception $e) {
            CLI::write('   ❌ Notification system error: ' . $e->getMessage(), 'red');
        }
    }

    private function testUserCreation()
    {
        CLI::write('7. Testing User Creation and RBAC...', 'yellow');
        
        try {
            $userModel = new UserModel();
            
            // Check if we can create a test user
            $testEmail = '<EMAIL>';
            
            // Clean up any existing test user
            $existingUser = $userModel->where('email', $testEmail)->first();
            if ($existingUser) {
                $userModel->delete($existingUser->id, true);
            }

            // Create test user
            $user = new \CodeIgniter\Shield\Entities\User([
                'username' => 'testauth',
                'email'    => $testEmail,
                'active'   => 1,
            ]);

            $userId = $userModel->save($user);
            
            if ($userId) {
                CLI::write('   ✅ User creation works', 'green');
                
                // Test role assignment
                $user = $userModel->find($userId);
                $user->addGroup('teacher');
                
                if ($user->inGroup('teacher')) {
                    CLI::write('   ✅ Role assignment works', 'green');
                } else {
                    CLI::write('   ❌ Role assignment failed', 'red');
                }

                // Test permissions
                if ($user->can('admin.access')) {
                    CLI::write('   ✅ Permission checking works', 'green');
                } else {
                    CLI::write('   ⚠️  Permission checking - teacher cannot access admin (expected)', 'yellow');
                }

                // Clean up test user
                $userModel->delete($userId, true);
                CLI::write('   ✅ Test user cleaned up', 'green');
                
            } else {
                CLI::write('   ❌ User creation failed', 'red');
            }
            
        } catch (\Exception $e) {
            CLI::write('   ❌ User creation test error: ' . $e->getMessage(), 'red');
        }
    }
}
