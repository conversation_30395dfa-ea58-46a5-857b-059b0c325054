# 🔐 Google OAuth Setup Guide - Student Management System

## 📍 **OAuth Callback URL**
```
http://studentwablas.me/auth/google/callback
```

## 🚀 **Step-by-Step Setup Instructions**

### 1. **Access Google Cloud Console**
- Go to: https://console.cloud.google.com/
- Sign in with your Google account

### 2. **Create or Select Project**
- Click on the project dropdown (top left)
- Either:
  - **Create New Project**: Click "New Project" → Enter "Student Management System" → Create
  - **Select Existing**: Choose your existing project

### 3. **Enable Required APIs**
- Go to "APIs & Services" → "Library"
- Search and enable these APIs:
  - ✅ **Google+ API** (for user profile)
  - ✅ **Google OAuth2 API** (for authentication)
  - ✅ **People API** (for user information)

### 4. **Configure OAuth Consent Screen**
- Go to "APIs & Services" → "OAuth consent screen"
- Choose **External** (for public access)
- Fill in required information:
  ```
  App name: Student Management System
  User support email: <EMAIL>
  Developer contact: <EMAIL>
  ```
- **Authorized domains**: Add `studentwablas.me`
- **Scopes**: Add these scopes:
  - `openid`
  - `email` 
  - `profile`

### 5. **Create OAuth 2.0 Credentials**
- Go to "APIs & Services" → "Credentials"
- Click "Create Credentials" → "OAuth 2.0 Client IDs"
- **Application type**: Web application
- **Name**: Student Management System OAuth
- **Authorized JavaScript origins**:
  ```
  http://studentwablas.me
  ```
- **Authorized redirect URIs**:
  ```
  http://studentwablas.me/auth/google/callback
  ```

### 6. **Copy Credentials**
After creating, you'll get:
- **Client ID**: `1234567890-abcdefghijklmnop.apps.googleusercontent.com`
- **Client Secret**: `GOCSPX-abcdefghijklmnopqrstuvwxyz`

### 7. **Update .env File**
Replace the placeholder values in your `.env` file:

```env
GOOGLE_CLIENT_ID = '1234567890-abcdefghijklmnop.apps.googleusercontent.com'
GOOGLE_CLIENT_SECRET = 'GOCSPX-abcdefghijklmnopqrstuvwxyz'
```

## 🔧 **Current Configuration Files**

### OAuth Configuration (`app/Config/OAuth.php`)
```php
public function __construct()
{
    parent::__construct();
    
    // Set Google OAuth configuration from environment
    $this->google['client_id']     = env('GOOGLE_CLIENT_ID', '');
    $this->google['client_secret'] = env('GOOGLE_CLIENT_SECRET', '');
    $this->google['redirect_uri']  = base_url('auth/google/callback');
}
```

### OAuth Routes (`app/Config/Routes.php`)
```php
// Custom Authentication Routes
$routes->group('auth', function($routes) {
    // Google OAuth
    $routes->get('google', 'GoogleAuthController::redirect');
    $routes->get('google/callback', 'GoogleAuthController::callback');
    
    // Other auth routes...
});
```

### OAuth Controller (`app/Controllers/GoogleAuthController.php`)
- **Redirect Method**: `/auth/google` → Redirects to Google
- **Callback Method**: `/auth/google/callback` → Handles Google response

## 🧪 **Testing OAuth Setup**

### 1. **Check Configuration**
Run the authentication test:
```bash
php spark test:auth-system
```

### 2. **Test OAuth Flow**
1. Visit: `http://studentwablas.me/auth/login`
2. Click "Continue with Google" button
3. Should redirect to Google OAuth consent screen
4. After authorization, should redirect back to your app

### 3. **Debug OAuth Issues**
Check server logs for OAuth errors:
```bash
tail -f writable/logs/log-*.php
```

## 🔍 **OAuth Flow Diagram**

```
User clicks "Continue with Google"
           ↓
http://studentwablas.me/auth/google
           ↓
GoogleAuthController::redirect()
           ↓
Redirect to Google OAuth
           ↓
User authorizes on Google
           ↓
Google redirects to callback
           ↓
http://studentwablas.me/auth/google/callback
           ↓
GoogleAuthController::callback()
           ↓
Create/Login user → Redirect to /admin
```

## 🛡️ **Security Considerations**

### Environment Variables
- ✅ **Never commit** real credentials to version control
- ✅ **Use .env file** for sensitive data
- ✅ **Rotate credentials** periodically

### OAuth Security
- ✅ **State parameter** validation (implemented)
- ✅ **HTTPS in production** (recommended)
- ✅ **Secure token storage** (implemented)
- ✅ **User data validation** (implemented)

## 🚨 **Common Issues & Solutions**

### Issue 1: "redirect_uri_mismatch"
**Solution**: Ensure the callback URL in Google Console exactly matches:
```
http://studentwablas.me/auth/google/callback
```

### Issue 2: "invalid_client"
**Solution**: Check that Client ID and Secret are correctly set in .env

### Issue 3: "access_denied"
**Solution**: User cancelled authorization - this is normal behavior

### Issue 4: OAuth not working
**Solution**: 
1. Clear browser cache
2. Check .env file has correct credentials
3. Restart development server
4. Check server logs for errors

## 📝 **Current .env Configuration**

Your `.env` file should have:
```env
#--------------------------------------------------------------------
# GOOGLE OAUTH CONFIGURATION
#--------------------------------------------------------------------
# IMPORTANT: Replace these with your actual Google OAuth credentials
# 
# OAuth Callback URL: http://studentwablas.me/auth/google/callback

GOOGLE_CLIENT_ID = 'your-google-client-id-here.apps.googleusercontent.com'
GOOGLE_CLIENT_SECRET = 'your-google-client-secret-here'
```

## 🎯 **Next Steps**

1. **Get Real Credentials**: Follow steps 1-6 above to get real Google OAuth credentials
2. **Update .env**: Replace placeholder values with real credentials
3. **Test OAuth**: Visit login page and test Google OAuth flow
4. **Production Setup**: For production, use HTTPS and update redirect URIs

## 📞 **Support**

If you encounter issues:
1. Check Google Cloud Console for error messages
2. Review server logs for detailed error information
3. Ensure all APIs are enabled in Google Cloud Console
4. Verify redirect URI matches exactly
5. Test with a fresh browser session

---

**🔗 Quick Links:**
- Google Cloud Console: https://console.cloud.google.com/
- OAuth Callback: http://studentwablas.me/auth/google/callback
- Login Page: http://studentwablas.me/auth/login
- Test Command: `php spark test:auth-system`
