<?php

namespace App\Controllers;

use App\Models\StudentApplyLeaveModel;
use App\Models\StudentSessionModel;
use App\Models\StudentsModel;
use App\Models\ClassesModel;
use App\Models\SectionsModel;

class StudentApplyLeaveController extends BaseCrudController
{
    protected $studentSessionModel;
    protected $studentsModel;
    protected $classesModel;
    protected $sectionsModel;

    public function __construct()
    {
        parent::__construct();
        $this->model = new StudentApplyLeaveModel();
        $this->studentSessionModel = new StudentSessionModel();
        $this->studentsModel = new StudentsModel();
        $this->classesModel = new ClassesModel();
        $this->sectionsModel = new SectionsModel();
        
        $this->viewPath = 'admin/student_apps/apply_leave';
        $this->routePrefix = 'admin/student-apps/apply-leave';
        $this->entityName = 'Leave Application';
        $this->entityNamePlural = 'Leave Applications';
    }

    /**
     * Display listing page
     */
    public function index()
    {
        $data = [
            'title' => $this->entityNamePlural . ' Management',
            'entity_name' => $this->entityName,
            'entity_name_plural' => $this->entityNamePlural,
            'route_prefix' => $this->routePrefix,
            'classes' => $this->classesModel->getForDropdown(),
            'sections' => $this->sectionsModel->getForDropdown(),
            'breadcrumbs' => [
                ['name' => 'Dashboard', 'url' => base_url('admin')],
                ['name' => 'Student Apps', 'url' => base_url('admin/student-apps')],
                ['name' => $this->entityNamePlural, 'url' => '']
            ]
        ];

        return view($this->viewPath . '/index', $data);
    }

    /**
     * Get form data for create/edit forms
     */
    protected function getFormData($record = null)
    {
        return [
            'student_sessions' => $this->getStudentSessionsForDropdown(),
            'classes' => $this->classesModel->getForDropdown(),
            'sections' => $this->sectionsModel->getForDropdown()
        ];
    }

    /**
     * Get student sessions for dropdown
     */
    private function getStudentSessionsForDropdown()
    {
        $sessions = $this->studentSessionModel->getStudentSessionWithDetails();
        $dropdown = [];

        foreach ($sessions as $session) {
            $label = $session['firstname'] . ' ' . $session['lastname'] . ' (' . $session['admission_no'] . ') - ' . $session['class'] . ' ' . $session['section'];
            $dropdown[$session['id']] = $label;
        }

        return $dropdown;
    }

    /**
     * Process form data before saving
     */
    protected function processFormData($data, $id = null)
    {
        // Set default values
        $data['status'] = $data['status'] ?? 0; // Pending by default
        $data['request_type'] = $data['request_type'] ?? 0; // Student request by default
        $data['apply_date'] = $data['apply_date'] ?? date('Y-m-d');

        // Validate date range
        if (!empty($data['from_date']) && !empty($data['to_date'])) {
            if (strtotime($data['from_date']) > strtotime($data['to_date'])) {
                throw new \Exception('From date cannot be greater than to date');
            }
        }

        // Check for overlapping leave dates
        if (!empty($data['student_session_id']) && !empty($data['from_date']) && !empty($data['to_date'])) {
            $hasOverlap = $this->model->checkOverlappingLeave(
                $data['student_session_id'], 
                $data['from_date'], 
                $data['to_date'], 
                $id
            );
            
            if ($hasOverlap) {
                throw new \Exception('Student already has approved leave for overlapping dates');
            }
        }

        return $data;
    }

    /**
     * Get data for DataTables
     */
    public function getData()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $draw = $this->request->getPost('draw');
        $start = $this->request->getPost('start');
        $length = $this->request->getPost('length');
        $searchValue = $this->request->getPost('search')['value'];
        $orderColumn = $this->request->getPost('order')[0]['column'];
        $orderDir = $this->request->getPost('order')[0]['dir'];

        // Get filters
        $filters = [
            'status' => $this->request->getPost('status'),
            'class_id' => $this->request->getPost('class_id'),
            'section_id' => $this->request->getPost('section_id'),
            'from_date' => $this->request->getPost('from_date'),
            'to_date' => $this->request->getPost('to_date')
        ];

        $builder = $this->model->getLeaveApplicationsWithDetails($filters);

        // Total records
        $totalRecords = $builder->countAllResults(false);

        // Search
        if (!empty($searchValue)) {
            $builder->groupStart()
                   ->like('students.firstname', $searchValue)
                   ->orLike('students.lastname', $searchValue)
                   ->orLike('students.admission_no', $searchValue)
                   ->orLike('student_applyleave.reason', $searchValue)
                   ->groupEnd();
        }

        // Filtered records
        $filteredRecords = $builder->countAllResults(false);

        // Order
        $columns = ['id', 'firstname', 'from_date', 'to_date', 'apply_date', 'status'];
        if (isset($columns[$orderColumn])) {
            $builder->orderBy($columns[$orderColumn], $orderDir);
        }

        // Limit
        $builder->limit($length, $start);
        $records = $builder->get()->getResultArray();

        $data = [];
        foreach ($records as $record) {
            $statusBadge = $this->getStatusBadge($record['status']);
            $actions = $this->getActionButtons($record);

            $data[] = [
                'id' => $record['id'],
                'student_name' => $record['firstname'] . ' ' . $record['lastname'],
                'admission_no' => $record['admission_no'],
                'class_section' => $record['class'] . ' - ' . $record['section'],
                'from_date' => date('M j, Y', strtotime($record['from_date'])),
                'to_date' => date('M j, Y', strtotime($record['to_date'])),
                'apply_date' => date('M j, Y', strtotime($record['apply_date'])),
                'reason' => substr($record['reason'], 0, 50) . (strlen($record['reason']) > 50 ? '...' : ''),
                'status' => $statusBadge,
                'actions' => $actions
            ];
        }

        return $this->response->setJSON([
            'draw' => $draw,
            'recordsTotal' => $totalRecords,
            'recordsFiltered' => $filteredRecords,
            'data' => $data
        ]);
    }

    /**
     * Approve leave application
     */
    public function approve($id)
    {
        if (!$this->request->isAJAX()) {
            return redirect()->back()->with('error', 'Invalid request');
        }

        $approvedBy = 1; // TODO: Get from session/auth
        $result = $this->model->approveLeave($id, $approvedBy);

        return $this->response->setJSON($result);
    }

    /**
     * Reject leave application
     */
    public function reject($id)
    {
        if (!$this->request->isAJAX()) {
            return redirect()->back()->with('error', 'Invalid request');
        }

        $approvedBy = 1; // TODO: Get from session/auth
        $result = $this->model->rejectLeave($id, $approvedBy);

        return $this->response->setJSON($result);
    }

    /**
     * Get statistics
     */
    public function getStats()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setStatusCode(403);
        }

        $stats = $this->model->getStatistics();

        return $this->response->setJSON([
            'success' => true,
            'data' => $stats
        ]);
    }

    /**
     * Get status badge HTML
     */
    private function getStatusBadge($status)
    {
        switch ($status) {
            case 0:
                return '<span class="inline-flex rounded-full bg-warning bg-opacity-10 py-1 px-3 text-sm font-medium text-warning">Pending</span>';
            case 1:
                return '<span class="inline-flex rounded-full bg-success bg-opacity-10 py-1 px-3 text-sm font-medium text-success">Approved</span>';
            case 2:
                return '<span class="inline-flex rounded-full bg-danger bg-opacity-10 py-1 px-3 text-sm font-medium text-danger">Rejected</span>';
            default:
                return '<span class="inline-flex rounded-full bg-gray bg-opacity-10 py-1 px-3 text-sm font-medium text-gray">Unknown</span>';
        }
    }

    /**
     * Get action buttons HTML
     */
    private function getActionButtons($record)
    {
        $buttons = '<div class="flex items-center space-x-3.5">';
        
        $buttons .= '<a href="' . base_url($this->routePrefix . '/show/' . $record['id']) . '" class="hover:text-primary" title="View">
                        <i class="fas fa-eye"></i>
                    </a>';

        if ($record['status'] == 0) { // Pending
            $buttons .= '<button onclick="approveLeave(' . $record['id'] . ')" class="hover:text-success" title="Approve">
                            <i class="fas fa-check"></i>
                        </button>';
            
            $buttons .= '<button onclick="rejectLeave(' . $record['id'] . ')" class="hover:text-danger" title="Reject">
                            <i class="fas fa-times"></i>
                        </button>';
            
            $buttons .= '<a href="' . base_url($this->routePrefix . '/edit/' . $record['id']) . '" class="hover:text-primary" title="Edit">
                            <i class="fas fa-edit"></i>
                        </a>';
        }

        $buttons .= '<button onclick="deleteRecord(' . $record['id'] . ')" class="hover:text-danger" title="Delete">
                        <i class="fas fa-trash"></i>
                    </button>';

        $buttons .= '</div>';

        return $buttons;
    }
}
