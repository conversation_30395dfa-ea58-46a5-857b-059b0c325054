<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Your Password - Student Management System</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f4f4f4;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 600;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        .content {
            padding: 40px 30px;
        }
        .alert-box {
            background-color: #fff5f5;
            border-left: 4px solid #ff6b6b;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .btn {
            display: inline-block;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            text-align: center;
            margin: 20px 0;
            transition: transform 0.2s;
        }
        .btn:hover {
            transform: translateY(-2px);
        }
        .footer {
            background-color: #f8f9fa;
            padding: 30px;
            text-align: center;
            color: #666;
            font-size: 14px;
        }
        .footer a {
            color: #ff6b6b;
            text-decoration: none;
        }
        .security-note {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
            color: #856404;
        }
        .icon {
            font-size: 48px;
            margin-bottom: 20px;
        }
        .timer {
            background-color: #f8f9ff;
            border: 2px solid #667eea;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div class="icon">🔐</div>
            <h1>Password Reset Request</h1>
            <p>Secure access to your account</p>
        </div>

        <!-- Content -->
        <div class="content">
            <h2>Hello <?= esc($user->username ?? 'User') ?>!</h2>
            
            <p>We received a request to reset the password for your Student Management System account. If you made this request, click the button below to reset your password:</p>

            <div class="alert-box">
                <h3>🚨 Password Reset Request</h3>
                <p><strong>Account:</strong> <?= esc($user->email ?? 'N/A') ?></p>
                <p><strong>Request Time:</strong> <?= date('F j, Y \a\t g:i A') ?></p>
                <p><strong>IP Address:</strong> <?= $_SERVER['REMOTE_ADDR'] ?? 'Unknown' ?></p>
            </div>

            <div style="text-align: center; margin: 30px 0;">
                <a href="<?= esc($link) ?>" class="btn">
                    🔑 Reset My Password
                </a>
            </div>

            <div class="timer">
                <h4>⏰ Time Sensitive</h4>
                <p>This password reset link will expire in <strong>1 hour</strong> for security reasons.</p>
            </div>

            <div class="security-note">
                <h4>🔒 Security Guidelines</h4>
                <ul>
                    <li><strong>If you didn't request this:</strong> Please ignore this email and your password will remain unchanged</li>
                    <li><strong>Keep it secure:</strong> Do not share this link with anyone</li>
                    <li><strong>One-time use:</strong> This link can only be used once</li>
                    <li><strong>Suspicious activity:</strong> Contact support if you suspect unauthorized access</li>
                </ul>
            </div>

            <p>If the button above doesn't work, you can copy and paste the following link into your browser:</p>
            <p style="word-break: break-all; background-color: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace;">
                <?= esc($link) ?>
            </p>

            <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">

            <h3>Password Security Tips</h3>
            <p>When creating your new password, make sure it:</p>
            <ul>
                <li>🔤 Contains at least 8 characters</li>
                <li>🔠 Includes uppercase and lowercase letters</li>
                <li>🔢 Contains at least one number</li>
                <li>🔣 Includes special characters (!@#$%^&*)</li>
                <li>🚫 Is not used on other websites</li>
                <li>🤐 Is not shared with anyone</li>
            </ul>

            <p>If you continue to have trouble accessing your account, please contact our support team for assistance.</p>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p><strong>Student Management System</strong></p>
            <p>This email was sent to <?= esc($user->email ?? 'you') ?> because a password reset was requested.</p>
            <p>
                <a href="<?= base_url() ?>">Visit our website</a> | 
                <a href="mailto:<EMAIL>">Contact Support</a> |
                <a href="<?= base_url('auth/login') ?>">Login Page</a>
            </p>
            <p style="margin-top: 20px; font-size: 12px; color: #999;">
                © <?= date('Y') ?> Student Management System. All rights reserved.<br>
                If you did not request this password reset, please ignore this email.
            </p>
        </div>
    </div>
</body>
</html>
