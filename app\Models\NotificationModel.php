<?php

namespace App\Models;

use CodeIgniter\Model;

class NotificationModel extends Model
{
    protected $table = 'notifications';
    protected $primaryKey = 'id';
    protected $useAutoIncrement = true;
    protected $returnType = 'array';
    protected $useSoftDeletes = false;
    protected $protectFields = true;
    protected $allowedFields = [
        'user_id',
        'title',
        'message',
        'type',
        'data',
        'read_at',
        'created_at',
        'updated_at'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat = 'datetime';
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    // Validation
    protected $validationRules = [
        'user_id' => 'required|integer',
        'title'   => 'required|max_length[255]',
        'message' => 'required',
        'type'    => 'required|in_list[info,success,warning,error,auth]',
    ];

    protected $validationMessages = [
        'user_id' => [
            'required' => 'User ID is required',
            'integer'  => 'User ID must be an integer',
        ],
        'title' => [
            'required'   => 'Title is required',
            'max_length' => 'Title cannot exceed 255 characters',
        ],
        'message' => [
            'required' => 'Message is required',
        ],
        'type' => [
            'required' => 'Type is required',
            'in_list'  => 'Type must be one of: info, success, warning, error, auth',
        ],
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert = [];
    protected $afterInsert = [];
    protected $beforeUpdate = [];
    protected $afterUpdate = [];
    protected $beforeFind = [];
    protected $afterFind = [];
    protected $beforeDelete = [];
    protected $afterDelete = [];

    /**
     * Create a notification for a user
     */
    public function createNotification(int $userId, string $title, string $message, string $type = 'info', array $data = []): bool
    {
        $notification = [
            'user_id' => $userId,
            'title'   => $title,
            'message' => $message,
            'type'    => $type,
            'data'    => !empty($data) ? json_encode($data) : null,
        ];

        return $this->insert($notification) !== false;
    }

    /**
     * Get unread notifications for a user
     */
    public function getUnreadForUser(int $userId): array
    {
        return $this->where('user_id', $userId)
                   ->where('read_at IS NULL')
                   ->orderBy('created_at', 'DESC')
                   ->findAll();
    }

    /**
     * Get all notifications for a user
     */
    public function getForUser(int $userId, int $limit = 50): array
    {
        return $this->where('user_id', $userId)
                   ->orderBy('created_at', 'DESC')
                   ->limit($limit)
                   ->findAll();
    }

    /**
     * Mark notification as read
     */
    public function markAsRead(int $notificationId): bool
    {
        return $this->update($notificationId, ['read_at' => date('Y-m-d H:i:s')]);
    }

    /**
     * Mark all notifications as read for a user
     */
    public function markAllAsReadForUser(int $userId): bool
    {
        return $this->where('user_id', $userId)
                   ->where('read_at IS NULL')
                   ->set(['read_at' => date('Y-m-d H:i:s')])
                   ->update();
    }

    /**
     * Get unread count for a user
     */
    public function getUnreadCountForUser(int $userId): int
    {
        return $this->where('user_id', $userId)
                   ->where('read_at IS NULL')
                   ->countAllResults();
    }

    /**
     * Delete old notifications (older than specified days)
     */
    public function deleteOldNotifications(int $days = 30): int
    {
        $cutoffDate = date('Y-m-d H:i:s', strtotime("-{$days} days"));
        
        return $this->where('created_at <', $cutoffDate)->delete();
    }

    /**
     * Create authentication-related notifications
     */
    public function createAuthNotification(int $userId, string $event, array $data = []): bool
    {
        $notifications = [
            'login' => [
                'title'   => 'New Login',
                'message' => 'You have successfully logged in to your account.',
                'type'    => 'auth',
            ],
            'register' => [
                'title'   => 'Welcome!',
                'message' => 'Your account has been created successfully. Welcome to Student Management System!',
                'type'    => 'success',
            ],
            'password_reset' => [
                'title'   => 'Password Reset',
                'message' => 'Your password has been reset successfully.',
                'type'    => 'auth',
            ],
            'email_verified' => [
                'title'   => 'Email Verified',
                'message' => 'Your email address has been verified successfully.',
                'type'    => 'success',
            ],
            'profile_updated' => [
                'title'   => 'Profile Updated',
                'message' => 'Your profile information has been updated successfully.',
                'type'    => 'info',
            ],
            'security_alert' => [
                'title'   => 'Security Alert',
                'message' => 'There was a security-related activity on your account.',
                'type'    => 'warning',
            ],
        ];

        if (!isset($notifications[$event])) {
            return false;
        }

        $notification = $notifications[$event];
        
        // Add additional data if provided
        if (!empty($data)) {
            $notification['message'] .= ' ' . ($data['message'] ?? '');
        }

        return $this->createNotification(
            $userId,
            $notification['title'],
            $notification['message'],
            $notification['type'],
            $data
        );
    }
}
