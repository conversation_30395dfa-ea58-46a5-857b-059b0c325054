<?= $this->extend('admin/layout') ?>

<?= $this->section('title') ?>Student Incident Management<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="mx-auto max-w-screen-2xl p-4 md:p-6 2xl:p-10">
    <!-- Breadcrumb -->
    <div class="mb-6 flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
        <h2 class="text-title-md2 font-semibold text-black dark:text-white">
            Student Incident Management
        </h2>
        <nav>
            <ol class="flex items-center gap-2">
                <?php foreach ($breadcrumbs as $index => $breadcrumb): ?>
                    <?php if ($index === count($breadcrumbs) - 1): ?>
                        <li class="text-primary"><?= $breadcrumb['name'] ?></li>
                    <?php else: ?>
                        <li><a class="font-medium" href="<?= $breadcrumb['url'] ?>"><?= $breadcrumb['name'] ?></a></li>
                        <li class="text-body-color">/</li>
                    <?php endif; ?>
                <?php endforeach; ?>
            </ol>
        </nav>
    </div>

    <!-- Action Buttons -->
    <div class="mb-6 flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between">
        <div class="flex gap-3">
            <a href="<?= base_url('admin/student-apps/incidents/create') ?>" class="inline-flex items-center justify-center rounded-md bg-primary py-2 px-4 text-center font-medium text-white hover:bg-opacity-90">
                <i class="fas fa-plus mr-2"></i>
                Add New Student Incident
            </a>
        </div>
    </div>

    <!-- Data Table -->
    <div class="rounded-sm border border-stroke bg-white shadow-default dark:border-strokedark dark:bg-boxdark">
        <div class="py-6 px-4 md:px-6 xl:px-7.5">
            <h4 class="text-xl font-semibold text-black dark:text-white">
                Student Incident List
            </h4>
        </div>

        <div class="p-4 md:p-6 xl:p-7.5">
            <div class="overflow-x-auto">
                <table id="dataTable" class="w-full table-auto">
                    <thead>
                        <tr class="bg-gray-2 text-left dark:bg-meta-4">
                            <th class="py-4 px-4 font-medium text-black dark:text-white">
                                <input type="checkbox" id="selectAll" class="rounded">
                            </th>
                            <th class="py-4 px-4 font-medium text-black dark:text-white">ID</th>
                            <th class="py-4 px-4 font-medium text-black dark:text-white">Details</th>
                            <th class="py-4 px-4 font-medium text-black dark:text-white">Status</th>
                            <th class="py-4 px-4 font-medium text-black dark:text-white">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Data will be loaded via AJAX -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Initialize DataTable
    const table = $('#dataTable').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '<?= base_url("admin/student-apps/incidents/get-data") ?>',
            type: 'POST'
        },
        columns: [
            { data: 'checkbox', orderable: false, searchable: false },
            { data: 'id' },
            { data: 'details' },
            { data: 'status' },
            { data: 'actions', orderable: false, searchable: false }
        ],
        order: [[1, 'desc']],
        pageLength: 25,
        responsive: true
    });
});
</script>
<?= $this->endSection() ?>