<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;

class FixStudentAppsLayouts extends BaseCommand
{
    protected $group       = 'Student Apps';
    protected $name        = 'fix:student-apps-layouts';
    protected $description = 'Fix incorrect layout references in Student Apps views';

    public function run(array $params)
    {
        CLI::write('Fixing Student Apps Layout References...', 'yellow');
        CLI::newLine();

        $this->fixLayoutReferences();

        CLI::newLine();
        CLI::write('✅ All layout references fixed!', 'green');
    }

    private function fixLayoutReferences()
    {
        CLI::write('1. Finding files with incorrect layout references...', 'yellow');
        
        $files = [
            'app/Views/admin/student_apps/apply_leave/edit.php',
            'app/Views/admin/student_apps/apply_leave/show.php',
            'app/Views/admin/student_apps/attendance/edit.php',
            'app/Views/admin/student_apps/attendance/show.php',
            'app/Views/admin/student_apps/behaviour/create.php',
            'app/Views/admin/student_apps/behaviour/edit.php',
            'app/Views/admin/student_apps/behaviour/show.php',
            'app/Views/admin/student_apps/documents/create.php',
            'app/Views/admin/student_apps/documents/edit.php',
            'app/Views/admin/student_apps/documents/show.php',
            'app/Views/admin/student_apps/fees/create.php',
            'app/Views/admin/student_apps/fees/edit.php',
            'app/Views/admin/student_apps/fees/show.php',
            'app/Views/admin/student_apps/incidents/create.php',
            'app/Views/admin/student_apps/incidents/edit.php',
            'app/Views/admin/student_apps/incidents/index.php',
            'app/Views/admin/student_apps/incidents/show.php',
            'app/Views/admin/student_apps/sessions/create.php',
            'app/Views/admin/student_apps/sessions/edit.php',
            'app/Views/admin/student_apps/sessions/show.php',
            'app/Views/admin/student_apps/subject_attendance/create.php',
            'app/Views/admin/student_apps/subject_attendance/edit.php',
            'app/Views/admin/student_apps/subject_attendance/show.php',
            'app/Views/admin/student_apps/timeline/create.php',
            'app/Views/admin/student_apps/timeline/edit.php',
            'app/Views/admin/student_apps/timeline/show.php',
            'app/Views/admin/student_apps/transport_fees/create.php',
            'app/Views/admin/student_apps/transport_fees/edit.php',
            'app/Views/admin/student_apps/transport_fees/show.php'
        ];

        $fixedCount = 0;
        $errorCount = 0;

        foreach ($files as $file) {
            $fullPath = ROOTPATH . $file;
            
            if (!file_exists($fullPath)) {
                CLI::write("   ❌ File not found: {$file}", 'red');
                $errorCount++;
                continue;
            }

            $content = file_get_contents($fullPath);
            $originalContent = $content;
            
            // Replace the incorrect layout reference
            $content = str_replace(
                "<?= \$this->extend('admin/layouts/main') ?>",
                "<?= \$this->extend('admin/layout') ?>",
                $content
            );

            if ($content !== $originalContent) {
                if (file_put_contents($fullPath, $content)) {
                    CLI::write("   ✅ Fixed: {$file}", 'green');
                    $fixedCount++;
                } else {
                    CLI::write("   ❌ Failed to write: {$file}", 'red');
                    $errorCount++;
                }
            } else {
                CLI::write("   ⚠️  No changes needed: {$file}", 'yellow');
            }
        }

        CLI::newLine();
        CLI::write("Summary:", 'cyan');
        CLI::write("  Fixed: {$fixedCount} files", 'green');
        if ($errorCount > 0) {
            CLI::write("  Errors: {$errorCount} files", 'red');
        }
    }
}
