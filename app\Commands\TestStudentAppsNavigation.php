<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;

class TestStudentAppsNavigation extends BaseCommand
{
    protected $group       = 'Student Apps';
    protected $name        = 'test:student-apps-navigation';
    protected $description = 'Test all Student Apps navigation links and functionality';

    public function run(array $params)
    {
        CLI::write('Testing Student Apps Navigation and Links...', 'yellow');
        CLI::newLine();

        $this->testMainDashboard();
        $this->testModuleLinks();
        $this->testSidebarNavigation();
        $this->testControllerMethods();
        $this->testRoutes();

        CLI::newLine();
        CLI::write('✅ All Student Apps navigation tests completed!', 'green');
    }

    private function testMainDashboard()
    {
        CLI::write('1. Testing Main Dashboard...', 'yellow');
        
        $url = base_url('admin/student-apps');
        $response = $this->makeRequest($url);
        
        if ($response['status'] === 200) {
            CLI::write('   ✅ Main dashboard accessible', 'green');
        } else {
            CLI::write('   ❌ Main dashboard failed: ' . $response['error'], 'red');
        }

        // Test stats endpoint
        $statsUrl = base_url('admin/student-apps/get-stats');
        $statsResponse = $this->makeRequest($statsUrl);
        
        if ($statsResponse['status'] === 200) {
            CLI::write('   ✅ Statistics endpoint working', 'green');
        } else {
            CLI::write('   ❌ Statistics endpoint failed: ' . $statsResponse['error'], 'red');
        }
    }

    private function testModuleLinks()
    {
        CLI::write('2. Testing Module Links...', 'yellow');
        
        $modules = [
            'apply-leave' => 'Apply Leave',
            'attendance' => 'Attendance',
            'behaviour' => 'Behaviour',
            'documents' => 'Documents',
            'fees' => 'Fees',
            'incidents' => 'Incidents',
            'sessions' => 'Sessions',
            'subject-attendance' => 'Subject Attendance',
            'timeline' => 'Timeline',
            'transport-fees' => 'Transport Fees'
        ];

        foreach ($modules as $route => $name) {
            $url = base_url("admin/student-apps/{$route}");
            $response = $this->makeRequest($url);
            
            if ($response['status'] === 200) {
                CLI::write("   ✅ {$name} module accessible", 'green');
            } else {
                CLI::write("   ❌ {$name} module failed: " . $response['error'], 'red');
            }
        }
    }

    private function testSidebarNavigation()
    {
        CLI::write('3. Testing Sidebar Navigation...', 'yellow');
        
        // Check if layout file contains all navigation links
        $layoutPath = APPPATH . 'Views/admin/layout.php';
        if (!file_exists($layoutPath)) {
            CLI::write('   ❌ Layout file not found', 'red');
            return;
        }

        $layoutContent = file_get_contents($layoutPath);
        
        $requiredLinks = [
            'admin/student-apps' => 'Dashboard',
            'admin/student-apps/apply-leave' => 'Apply Leave',
            'admin/student-apps/attendance' => 'Attendance',
            'admin/student-apps/behaviour' => 'Behaviour',
            'admin/student-apps/documents' => 'Documents',
            'admin/student-apps/fees' => 'Fees',
            'admin/student-apps/incidents' => 'Incidents',
            'admin/student-apps/sessions' => 'Sessions',
            'admin/student-apps/subject-attendance' => 'Subject Attendance',
            'admin/student-apps/timeline' => 'Timeline',
            'admin/student-apps/transport-fees' => 'Transport Fees'
        ];

        foreach ($requiredLinks as $link => $name) {
            if (strpos($layoutContent, $link) !== false) {
                CLI::write("   ✅ {$name} link found in sidebar", 'green');
            } else {
                CLI::write("   ❌ {$name} link missing from sidebar", 'red');
            }
        }
    }

    private function testControllerMethods()
    {
        CLI::write('4. Testing Controller Methods...', 'yellow');
        
        $controllers = [
            'StudentAppsController' => ['index', 'getStats'],
            'StudentApplyLeaveController' => ['index', 'create', 'getData'],
            'StudentAttendanceController' => ['index', 'create', 'getData'],
            'StudentBehaviourController' => ['index', 'create', 'getData'],
            'StudentDocController' => ['index', 'create', 'getData'],
            'StudentFeesController' => ['index', 'create', 'getData'],
            'StudentIncidentsController' => ['index', 'create', 'getData'],
            'StudentSessionController' => ['index', 'create', 'getData'],
            'StudentSubjectAttendanceController' => ['index', 'create', 'getData'],
            'StudentTimelineController' => ['index', 'create', 'getData'],
            'StudentTransportFeesController' => ['index', 'create', 'getData']
        ];

        foreach ($controllers as $controllerName => $methods) {
            $controllerPath = APPPATH . "Controllers/{$controllerName}.php";
            
            if (file_exists($controllerPath)) {
                CLI::write("   ✅ {$controllerName} exists", 'green');
                
                $controllerContent = file_get_contents($controllerPath);
                foreach ($methods as $method) {
                    if (strpos($controllerContent, "function {$method}(") !== false) {
                        CLI::write("     ✅ Method {$method} exists", 'green');
                    } else {
                        CLI::write("     ❌ Method {$method} missing", 'red');
                    }
                }
            } else {
                CLI::write("   ❌ {$controllerName} not found", 'red');
            }
        }
    }

    private function testRoutes()
    {
        CLI::write('5. Testing Routes Configuration...', 'yellow');
        
        $routesPath = APPPATH . 'Config/Routes.php';
        if (!file_exists($routesPath)) {
            CLI::write('   ❌ Routes file not found', 'red');
            return;
        }

        $routesContent = file_get_contents($routesPath);
        
        $requiredRoutes = [
            'student-apps' => 'Student Apps group',
            'apply-leave' => 'Apply Leave routes',
            'attendance' => 'Attendance routes',
            'behaviour' => 'Behaviour routes',
            'documents' => 'Documents routes',
            'fees' => 'Fees routes',
            'incidents' => 'Incidents routes',
            'sessions' => 'Sessions routes',
            'subject-attendance' => 'Subject Attendance routes',
            'timeline' => 'Timeline routes',
            'transport-fees' => 'Transport Fees routes'
        ];

        foreach ($requiredRoutes as $route => $description) {
            if (strpos($routesContent, $route) !== false) {
                CLI::write("   ✅ {$description} configured", 'green');
            } else {
                CLI::write("   ❌ {$description} missing", 'red');
            }
        }
    }

    private function makeRequest($url)
    {
        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);
            
            if ($error) {
                return ['status' => 0, 'error' => $error];
            }
            
            return ['status' => $httpCode, 'response' => $response, 'error' => null];
        } catch (\Exception $e) {
            return ['status' => 0, 'error' => $e->getMessage()];
        }
    }
}
