<?php

namespace Config;

use CodeIgniter\Config\BaseConfig;

class OAuth extends BaseConfig
{
    /**
     * Google OAuth Configuration
     */
    public array $google = [
        'client_id'     => '',  // Set in .env file
        'client_secret' => '',  // Set in .env file
        'redirect_uri'  => '',  // Will be set dynamically
        'scopes'        => [
            'openid',
            'email',
            'profile'
        ],
    ];

    /**
     * OAuth Providers Configuration
     */
    public array $providers = [
        'google' => [
            'name'        => 'Google',
            'icon'        => 'fab fa-google',
            'color'       => '#db4437',
            'enabled'     => true,
            'description' => 'Sign in with your Google account',
        ],
    ];

    /**
     * Default user role for OAuth registrations
     */
    public string $defaultRole = 'teacher';

    /**
     * Allow automatic user creation for OAuth logins
     */
    public bool $allowAutoRegistration = true;

    /**
     * Require email verification for OAuth users
     */
    public bool $requireEmailVerification = false;

    /**
     * Session keys for OAuth state management
     */
    public array $sessionKeys = [
        'state'        => 'oauth_state',
        'redirect_url' => 'oauth_redirect_url',
        'provider'     => 'oauth_provider',
    ];

    public function __construct()
    {
        parent::__construct();

        // Set Google OAuth configuration from environment
        $this->google['client_id']     = env('GOOGLE_CLIENT_ID', '');
        $this->google['client_secret'] = env('GOOGLE_CLIENT_SECRET', '');
        $this->google['redirect_uri']  = base_url('auth/google/callback');
    }
}
