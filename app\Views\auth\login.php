<?= $this->extend('auth/layout') ?>

<?= $this->section('title') ?>Login<?= $this->endSection() ?>

<?= $this->section('main') ?>
<div class="space-y-6">
    <!-- Header -->
    <div class="text-center">
        <h2 class="text-2xl font-bold text-gray-900 mb-2">Welcome Back</h2>
        <p class="text-gray-600">Sign in to your account to continue</p>
    </div>

    <!-- Login Form -->
    <?= form_open(route_to('login'), ['class' => 'space-y-6']) ?>
        
        <!-- Email Field -->
        <div class="space-y-2">
            <label for="email" class="block text-sm font-medium text-gray-700">
                <i class="fas fa-envelope mr-2 text-gray-400"></i>Email Address
            </label>
            <div class="relative">
                <input 
                    type="email" 
                    id="email" 
                    name="email" 
                    value="<?= old('email') ?>"
                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200 pl-12"
                    placeholder="Enter your email address"
                    required
                    autocomplete="email"
                >
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <i class="fas fa-envelope text-gray-400"></i>
                </div>
            </div>
            <?php if (has_error('email')): ?>
                <p class="text-red-500 text-sm mt-1">
                    <i class="fas fa-exclamation-circle mr-1"></i><?= error('email') ?>
                </p>
            <?php endif; ?>
        </div>

        <!-- Password Field -->
        <div class="space-y-2">
            <label for="password" class="block text-sm font-medium text-gray-700">
                <i class="fas fa-lock mr-2 text-gray-400"></i>Password
            </label>
            <div class="relative">
                <input 
                    type="password" 
                    id="password" 
                    name="password" 
                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200 pl-12 pr-12"
                    placeholder="Enter your password"
                    required
                    autocomplete="current-password"
                >
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <i class="fas fa-lock text-gray-400"></i>
                </div>
                <button 
                    type="button" 
                    class="absolute inset-y-0 right-0 pr-3 flex items-center"
                    onclick="togglePassword('password')"
                >
                    <i class="fas fa-eye text-gray-400 hover:text-gray-600" id="password-toggle"></i>
                </button>
            </div>
            <?php if (has_error('password')): ?>
                <p class="text-red-500 text-sm mt-1">
                    <i class="fas fa-exclamation-circle mr-1"></i><?= error('password') ?>
                </p>
            <?php endif; ?>
        </div>

        <!-- Remember Me & Forgot Password -->
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <input 
                    type="checkbox" 
                    id="remember" 
                    name="remember" 
                    class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                    <?= old('remember') ? 'checked' : '' ?>
                >
                <label for="remember" class="ml-2 block text-sm text-gray-700">
                    Remember me
                </label>
            </div>
            <a href="<?= route_to('magic-link') ?>" class="text-sm text-primary-600 hover:text-primary-500 transition-colors duration-200">
                Forgot password?
            </a>
        </div>

        <!-- Login Button -->
        <button 
            type="submit" 
            class="w-full btn-gradient text-white py-3 px-4 rounded-lg font-medium hover:shadow-lg transition-all duration-200 flex items-center justify-center"
        >
            <i class="fas fa-sign-in-alt mr-2"></i>
            Sign In
        </button>

    <?= form_close() ?>

    <!-- Divider -->
    <div class="relative">
        <div class="absolute inset-0 flex items-center">
            <div class="w-full border-t border-gray-300"></div>
        </div>
        <div class="relative flex justify-center text-sm">
            <span class="px-2 bg-white text-gray-500">Or continue with</span>
        </div>
    </div>

    <!-- Google OAuth Button -->
    <a 
        href="<?= base_url('auth/google') ?>" 
        class="w-full social-btn bg-white border border-gray-300 text-gray-700 py-3 px-4 rounded-lg font-medium hover:bg-gray-50 transition-all duration-200 flex items-center justify-center"
    >
        <svg class="w-5 h-5 mr-3" viewBox="0 0 24 24">
            <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
            <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
            <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
            <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
        </svg>
        Continue with Google
    </a>

    <!-- Register Link -->
    <div class="text-center">
        <p class="text-gray-600">
            Don't have an account? 
            <a href="<?= route_to('register') ?>" class="text-primary-600 hover:text-primary-500 font-medium transition-colors duration-200">
                Sign up here
            </a>
        </p>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const toggle = document.getElementById(fieldId + '-toggle');
    
    if (field.type === 'password') {
        field.type = 'text';
        toggle.classList.remove('fa-eye');
        toggle.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        toggle.classList.remove('fa-eye-slash');
        toggle.classList.add('fa-eye');
    }
}

// Auto-focus first input
document.addEventListener('DOMContentLoaded', function() {
    const firstInput = document.querySelector('input[type="email"]');
    if (firstInput) {
        firstInput.focus();
    }
});
</script>
<?= $this->endSection() ?>
