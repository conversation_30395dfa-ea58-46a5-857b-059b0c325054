<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;

class TestStudentAppsComplete extends BaseCommand
{
    protected $group       = 'Student Apps';
    protected $name        = 'test:student-apps-complete';
    protected $description = 'Complete test of all Student Apps functionality including create pages';

    public function run(array $params)
    {
        CLI::write('Complete Student Apps Functionality Test...', 'yellow');
        CLI::newLine();

        $this->testMainPages();
        $this->testCreatePages();
        $this->testLayoutReferences();

        CLI::newLine();
        CLI::write('✅ Complete Student Apps test finished!', 'green');
    }

    private function testMainPages()
    {
        CLI::write('1. Testing Main Module Pages...', 'yellow');
        
        $modules = [
            'apply-leave' => 'Apply Leave',
            'attendance' => 'Attendance',
            'behaviour' => 'Behaviour',
            'documents' => 'Documents',
            'fees' => 'Fees',
            'incidents' => 'Incidents',
            'sessions' => 'Sessions',
            'subject-attendance' => 'Subject Attendance',
            'timeline' => 'Timeline',
            'transport-fees' => 'Transport Fees'
        ];

        foreach ($modules as $route => $name) {
            $url = base_url("admin/student-apps/{$route}");
            $response = $this->makeRequest($url);
            
            if ($response['status'] === 200) {
                CLI::write("   ✅ {$name} index page working", 'green');
            } else {
                CLI::write("   ❌ {$name} index page failed: " . $response['error'], 'red');
            }
        }
    }

    private function testCreatePages()
    {
        CLI::write('2. Testing Create Pages...', 'yellow');
        
        $modules = [
            'apply-leave' => 'Apply Leave',
            'attendance' => 'Attendance',
            'behaviour' => 'Behaviour',
            'documents' => 'Documents',
            'fees' => 'Fees',
            'incidents' => 'Incidents',
            'sessions' => 'Sessions',
            'subject-attendance' => 'Subject Attendance',
            'timeline' => 'Timeline',
            'transport-fees' => 'Transport Fees'
        ];

        foreach ($modules as $route => $name) {
            $url = base_url("admin/student-apps/{$route}/create");
            $response = $this->makeRequest($url);
            
            if ($response['status'] === 200) {
                CLI::write("   ✅ {$name} create page working", 'green');
            } else {
                CLI::write("   ❌ {$name} create page failed: " . $response['error'], 'red');
            }
        }
    }

    private function testLayoutReferences()
    {
        CLI::write('3. Testing Layout References...', 'yellow');
        
        $viewsDir = APPPATH . 'Views/admin/student_apps/';
        $incorrectReferences = 0;
        $totalFiles = 0;

        $iterator = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($viewsDir)
        );

        foreach ($iterator as $file) {
            if ($file->isFile() && $file->getExtension() === 'php') {
                $totalFiles++;
                $content = file_get_contents($file->getPathname());
                
                if (strpos($content, "admin/layouts/main") !== false) {
                    $incorrectReferences++;
                    $relativePath = str_replace(APPPATH . 'Views/', '', $file->getPathname());
                    CLI::write("   ❌ Incorrect layout reference: {$relativePath}", 'red');
                }
            }
        }

        if ($incorrectReferences === 0) {
            CLI::write("   ✅ All {$totalFiles} view files have correct layout references", 'green');
        } else {
            CLI::write("   ❌ Found {$incorrectReferences} files with incorrect layout references", 'red');
        }
    }

    private function makeRequest($url)
    {
        try {
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_USERAGENT, 'Student Apps Test');
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $error = curl_error($ch);
            curl_close($ch);
            
            if ($error) {
                return ['status' => 0, 'error' => $error];
            }
            
            return ['status' => $httpCode, 'response' => $response, 'error' => null];
        } catch (\Exception $e) {
            return ['status' => 0, 'error' => $e->getMessage()];
        }
    }
}
