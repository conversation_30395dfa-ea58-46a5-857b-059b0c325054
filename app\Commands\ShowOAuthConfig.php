<?php

namespace App\Commands;

use CodeIgniter\CLI\BaseCommand;
use CodeIgniter\CLI\CLI;
use Config\OAuth;

class ShowOAuthConfig extends BaseCommand
{
    protected $group       = 'Authentication';
    protected $name        = 'show:oauth-config';
    protected $description = 'Display current OAuth configuration and setup status';

    public function run(array $params)
    {
        CLI::write('🔐 Google OAuth Configuration Status', 'yellow');
        CLI::newLine();

        $this->showOAuthURLs();
        $this->showEnvironmentConfig();
        $this->showOAuthConfig();
        $this->showSetupInstructions();

        CLI::newLine();
        CLI::write('✅ OAuth configuration check completed!', 'green');
    }

    private function showOAuthURLs()
    {
        CLI::write('📍 OAuth URLs:', 'cyan');
        CLI::write('   Login Page:     ' . base_url('auth/login'), 'white');
        CLI::write('   Google OAuth:   ' . base_url('auth/google'), 'white');
        CLI::write('   OAuth Callback: ' . base_url('auth/google/callback'), 'white');
        CLI::newLine();
    }

    private function showEnvironmentConfig()
    {
        CLI::write('🔧 Environment Configuration:', 'cyan');
        
        $clientId = env('GOOGLE_CLIENT_ID', '');
        $clientSecret = env('GOOGLE_CLIENT_SECRET', '');
        
        if (!empty($clientId) && $clientId !== 'your-google-client-id-here.apps.googleusercontent.com') {
            CLI::write('   ✅ GOOGLE_CLIENT_ID: ' . substr($clientId, 0, 20) . '...', 'green');
        } else {
            CLI::write('   ❌ GOOGLE_CLIENT_ID: Not configured (using placeholder)', 'red');
        }
        
        if (!empty($clientSecret) && $clientSecret !== 'your-google-client-secret-here') {
            CLI::write('   ✅ GOOGLE_CLIENT_SECRET: ' . str_repeat('*', strlen($clientSecret)), 'green');
        } else {
            CLI::write('   ❌ GOOGLE_CLIENT_SECRET: Not configured (using placeholder)', 'red');
        }
        
        CLI::newLine();
    }

    private function showOAuthConfig()
    {
        CLI::write('⚙️  OAuth Configuration:', 'cyan');
        
        try {
            $oauthConfig = new OAuth();
            
            CLI::write('   Callback URL: ' . $oauthConfig->google['redirect_uri'], 'white');
            CLI::write('   Auto Registration: ' . ($oauthConfig->allowAutoRegistration ? 'Enabled' : 'Disabled'), 'white');
            CLI::write('   Email Verification: ' . ($oauthConfig->requireEmailVerification ? 'Required' : 'Not Required'), 'white');
            CLI::write('   Default Role: ' . $oauthConfig->defaultRole, 'white');
            
            // Show scopes
            CLI::write('   OAuth Scopes:', 'white');
            foreach ($oauthConfig->google['scopes'] as $scope) {
                CLI::write('     - ' . $scope, 'white');
            }
            
        } catch (\Exception $e) {
            CLI::write('   ❌ Error loading OAuth config: ' . $e->getMessage(), 'red');
        }
        
        CLI::newLine();
    }

    private function showSetupInstructions()
    {
        CLI::write('📋 Setup Instructions:', 'cyan');
        
        $clientId = env('GOOGLE_CLIENT_ID', '');
        $clientSecret = env('GOOGLE_CLIENT_SECRET', '');
        
        if (empty($clientId) || $clientId === 'your-google-client-id-here.apps.googleusercontent.com' ||
            empty($clientSecret) || $clientSecret === 'your-google-client-secret-here') {
            
            CLI::write('   🚨 Google OAuth is NOT configured. Follow these steps:', 'yellow');
            CLI::newLine();
            CLI::write('   1. Go to Google Cloud Console:', 'white');
            CLI::write('      https://console.cloud.google.com/', 'blue');
            CLI::newLine();
            CLI::write('   2. Create OAuth 2.0 credentials with this callback URL:', 'white');
            CLI::write('      ' . base_url('auth/google/callback'), 'blue');
            CLI::newLine();
            CLI::write('   3. Update your .env file with real credentials:', 'white');
            CLI::write('      GOOGLE_CLIENT_ID = \'your-real-client-id\'', 'blue');
            CLI::write('      GOOGLE_CLIENT_SECRET = \'your-real-client-secret\'', 'blue');
            CLI::newLine();
            CLI::write('   4. Restart your development server:', 'white');
            CLI::write('      php spark serve --host studentwablas.me --port 80', 'blue');
            CLI::newLine();
            CLI::write('   📖 For detailed instructions, see: GOOGLE_OAUTH_SETUP.md', 'yellow');
            
        } else {
            CLI::write('   ✅ Google OAuth appears to be configured!', 'green');
            CLI::write('   🧪 Test the OAuth flow:', 'white');
            CLI::write('      1. Visit: ' . base_url('auth/login'), 'blue');
            CLI::write('      2. Click "Continue with Google"', 'blue');
            CLI::write('      3. Complete Google authorization', 'blue');
            CLI::write('      4. Should redirect to admin dashboard', 'blue');
        }
    }
}
