<?= $this->extend('auth/layout') ?>

<?= $this->section('title') ?>Reset Password<?= $this->endSection() ?>

<?= $this->section('main') ?>
<div class="space-y-6">
    <!-- Header -->
    <div class="text-center">
        <div class="flex justify-center mb-4">
            <div class="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center">
                <i class="fas fa-key text-2xl text-orange-600"></i>
            </div>
        </div>
        <h2 class="text-2xl font-bold text-gray-900 mb-2">Reset Password</h2>
        <p class="text-gray-600">Enter your email to receive a password reset link</p>
    </div>

    <!-- Reset Form -->
    <?= form_open(route_to('magic-link'), ['class' => 'space-y-6']) ?>
        
        <!-- Email Field -->
        <div class="space-y-2">
            <label for="email" class="block text-sm font-medium text-gray-700">
                <i class="fas fa-envelope mr-2 text-gray-400"></i>Email Address
            </label>
            <div class="relative">
                <input 
                    type="email" 
                    id="email" 
                    name="email" 
                    value="<?= old('email') ?>"
                    class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200 pl-12"
                    placeholder="Enter your email address"
                    required
                    autocomplete="email"
                >
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <i class="fas fa-envelope text-gray-400"></i>
                </div>
            </div>
            <?php if (has_error('email')): ?>
                <p class="text-red-500 text-sm mt-1">
                    <i class="fas fa-exclamation-circle mr-1"></i><?= error('email') ?>
                </p>
            <?php endif; ?>
        </div>

        <!-- Submit Button -->
        <button 
            type="submit" 
            class="w-full btn-gradient text-white py-3 px-4 rounded-lg font-medium hover:shadow-lg transition-all duration-200 flex items-center justify-center"
        >
            <i class="fas fa-paper-plane mr-2"></i>
            Send Reset Link
        </button>

    <?= form_close() ?>

    <!-- Info Box -->
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div class="flex items-start">
            <i class="fas fa-info-circle text-blue-500 mt-1 mr-3"></i>
            <div class="text-left">
                <h3 class="text-sm font-medium text-blue-800 mb-1">How it works</h3>
                <p class="text-sm text-blue-700">
                    We'll send you a secure link that will allow you to reset your password. 
                    The link will expire in 1 hour for security reasons.
                </p>
            </div>
        </div>
    </div>

    <!-- Back to Login -->
    <div class="text-center">
        <a 
            href="<?= route_to('login') ?>" 
            class="text-primary-600 hover:text-primary-500 font-medium transition-colors duration-200 flex items-center justify-center"
        >
            <i class="fas fa-arrow-left mr-2"></i>
            Back to Login
        </a>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
// Auto-focus email input
document.addEventListener('DOMContentLoaded', function() {
    const emailInput = document.querySelector('input[type="email"]');
    if (emailInput) {
        emailInput.focus();
    }
});
</script>
<?= $this->endSection() ?>
