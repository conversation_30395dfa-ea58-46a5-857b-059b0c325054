/**
 * Admin Dashboard JavaScript Functions
 * Modern, responsive, and feature-rich admin panel
 */

// Global admin object
const Admin = {
    // Configuration
    config: {
        baseUrl: window.location.origin,
        apiUrl: window.location.origin + '/api',
        csrfToken: document.querySelector('meta[name="csrf-token"]')?.getAttribute('content'),
        theme: localStorage.getItem('admin-theme') || 'light'
    },

    // Initialize admin panel
    init() {
        this.setupEventListeners();
        this.initializeComponents();
        this.loadTheme();
        this.initSidebar();
        console.log('Admin Dashboard Initialized');
    },

    // Setup global event listeners
    setupEventListeners() {
        // Sidebar toggle for mobile
        document.addEventListener('click', (e) => {
            if (e.target.matches('#sidebar-toggle') || e.target.closest('#sidebar-toggle')) {
                this.toggleSidebar();
            }
        });

        // Close sidebar on overlay click
        document.addEventListener('click', (e) => {
            if (e.target.matches('#sidebar-overlay')) {
                this.closeSidebar();
            }
        });
        
        // Sidebar minimize toggle for desktop
        document.addEventListener('click', (e) => {
            if (e.target.matches('#sidebar-minimize') || e.target.closest('#sidebar-minimize')) {
                this.toggleSidebarMinimize();
            }
        });
        
        // Submenu toggle
        document.addEventListener('click', (e) => {
            if (e.target.matches('.menu-item-parent') || e.target.closest('.menu-item-parent')) {
                const menuItem = e.target.matches('.menu-item-parent') ? e.target : e.target.closest('.menu-item-parent');
                this.toggleSubmenu(menuItem);
            }
        });

        // Escape key to close modals/sidebar
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closeSidebar();
            }
        });

        // Auto-save forms
        document.addEventListener('input', (e) => {
            if (e.target.hasAttribute('data-auto-save')) {
                this.autoSave(e.target);
            }
        });

        // Confirm delete actions
        document.addEventListener('click', (e) => {
            if (e.target.hasAttribute('data-confirm-delete')) {
                e.preventDefault();
                this.confirmDelete(e.target);
            }
        });
    },

    // Initialize components
    initializeComponents() {
        this.initDataTables();
        this.initTooltips();
        this.initProgressBars();
        this.initCounters();
    },

    // Sidebar functions
    toggleSidebar() {
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('sidebar-overlay');
        
        if (sidebar && overlay) {
            sidebar.classList.toggle('-translate-x-full');
            overlay.classList.toggle('hidden');
        }
    },

    closeSidebar() {
        const sidebar = document.getElementById('sidebar');
        const overlay = document.getElementById('sidebar-overlay');
        
        if (sidebar && overlay) {
            sidebar.classList.add('-translate-x-full');
            overlay.classList.add('hidden');
        }
    },
    
    toggleSidebarMinimize() {
        const sidebar = document.getElementById('sidebar');
        const minimizeBtn = document.getElementById('sidebar-minimize');
        
        if (sidebar) {
            sidebar.classList.toggle('sidebar-minimized');
            
            // Toggle the icon direction
            if (minimizeBtn) {
                const icon = minimizeBtn.querySelector('i');
                if (icon) {
                    if (sidebar.classList.contains('sidebar-minimized')) {
                        icon.classList.remove('fa-chevron-left');
                        icon.classList.add('fa-chevron-right');
                    } else {
                        icon.classList.remove('fa-chevron-right');
                        icon.classList.add('fa-chevron-left');
                    }
                }
            }
            
            // Store the state in localStorage
            this.storage.set('sidebar-minimized', sidebar.classList.contains('sidebar-minimized'));
        }
    },
    
    toggleSubmenu(menuItem) {
        if (!menuItem) return;
        
        const submenu = menuItem.nextElementSibling;
        const arrow = menuItem.querySelector('.menu-arrow');
        const submenus = document.querySelectorAll('.submenu');
      
        if (submenu && submenu.classList.contains('submenu')) {
          // Toggle submenu visibility
          submenu.classList.toggle('open');
         
          // Rotate arrow icon
          if (arrow) {
            arrow.classList.toggle('rotate');
          }
         
          // Close other open submenus
          submenus.forEach(otherSubmenu => {
            if (otherSubmenu !== submenu) {
              otherSubmenu.classList.remove('open');
              const otherArrow = otherSubmenu.previousElementSibling?.querySelector('.menu-arrow');
              if (otherArrow) otherArrow.classList.remove('rotate');
            }
          });
        }
    },
    
    initSidebar() {
        // Load saved sidebar state
        const isMinimized = this.storage.get('sidebar-minimized');
        if (isMinimized) {
            const sidebar = document.getElementById('sidebar');
            const minimizeBtn = document.getElementById('sidebar-minimize');
            
            if (sidebar) {
                sidebar.classList.add('sidebar-minimized');
            }
            
            if (minimizeBtn) {
                const icon = minimizeBtn.querySelector('i');
                if (icon) {
                    icon.classList.remove('fa-chevron-left');
                    icon.classList.add('fa-chevron-right');
                }
            }
        }
        
        // Check current URL and open corresponding submenu
        const currentPath = window.location.pathname;
        const navLinks = document.querySelectorAll('.nav-link');
        
        navLinks.forEach(link => {
            if (link.getAttribute('href') === currentPath) {
                // If link is in submenu, open the parent menu
                const parentSubmenu = link.closest('.submenu');
                if (parentSubmenu) {
                    parentSubmenu.classList.add('open');
                    const parentItem = parentSubmenu.previousElementSibling;
                    const arrow = parentItem?.querySelector('.menu-arrow');
                    if (arrow) arrow.classList.add('rotate');
                }
                
                // Add active class to the link
                link.classList.add('active');
            }
        });
    },

    // Theme management
    loadTheme() {
        if (this.config.theme === 'dark') {
            document.documentElement.classList.add('dark');
        }
    },

    toggleTheme() {
        const isDark = document.documentElement.classList.contains('dark');
        if (isDark) {
            document.documentElement.classList.remove('dark');
            this.config.theme = 'light';
        } else {
            document.documentElement.classList.add('dark');
            this.config.theme = 'dark';
        }
        localStorage.setItem('admin-theme', this.config.theme);
    },

    // DataTables initialization
    initDataTables() {
        if (typeof $ !== 'undefined' && $.fn.DataTable) {
            $('.data-table').each(function() {
                if (!$.fn.DataTable.isDataTable(this)) {
                    $(this).DataTable({
                        responsive: true,
                        pageLength: 25,
                        lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "All"]],
                        language: {
                            search: "_INPUT_",
                            searchPlaceholder: "Search...",
                            lengthMenu: "Show _MENU_ entries",
                            info: "Showing _START_ to _END_ of _TOTAL_ entries",
                            infoEmpty: "Showing 0 to 0 of 0 entries",
                            infoFiltered: "(filtered from _MAX_ total entries)"
                        },
                        dom: '<"flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4"<"mb-2 sm:mb-0"l><"mb-2 sm:mb-0"f>>rtip'
                    });
                }
            });
        }
    },

    // Tooltip initialization
    initTooltips() {
        document.querySelectorAll('[data-tooltip]').forEach(element => {
            element.addEventListener('mouseenter', (e) => {
                this.showTooltip(e.target, e.target.getAttribute('data-tooltip'));
            });
            element.addEventListener('mouseleave', () => {
                this.hideTooltip();
            });
        });
    },

    // Show tooltip
    showTooltip(element, text) {
        const tooltip = document.createElement('div');
        tooltip.className = 'absolute z-50 px-2 py-1 text-xs text-white bg-gray-900 rounded shadow-lg';
        tooltip.textContent = text;
        tooltip.id = 'admin-tooltip';
        
        document.body.appendChild(tooltip);
        
        const rect = element.getBoundingClientRect();
        tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
        tooltip.style.top = rect.top - tooltip.offsetHeight - 5 + 'px';
    },

    // Hide tooltip
    hideTooltip() {
        const tooltip = document.getElementById('admin-tooltip');
        if (tooltip) {
            tooltip.remove();
        }
    },

    // Progress bar animation
    initProgressBars() {
        document.querySelectorAll('.progress-bar').forEach(bar => {
            const fill = bar.querySelector('.progress-fill');
            const percentage = fill.getAttribute('data-percentage') || 0;
            
            setTimeout(() => {
                fill.style.width = percentage + '%';
            }, 100);
        });
    },

    // Counter animation
    initCounters() {
        document.querySelectorAll('[data-counter]').forEach(counter => {
            const target = parseInt(counter.getAttribute('data-counter'));
            const duration = parseInt(counter.getAttribute('data-duration')) || 2000;
            this.animateCounter(counter, target, duration);
        });
    },

    // Animate counter
    animateCounter(element, target, duration) {
        let start = 0;
        const increment = target / (duration / 16);
        
        const timer = setInterval(() => {
            start += increment;
            element.textContent = Math.floor(start).toLocaleString();
            
            if (start >= target) {
                element.textContent = target.toLocaleString();
                clearInterval(timer);
            }
        }, 16);
    },

    // Auto-save functionality
    autoSave(element) {
        clearTimeout(element.autoSaveTimer);
        element.autoSaveTimer = setTimeout(() => {
            const formData = new FormData();
            formData.append(element.name, element.value);
            formData.append('_token', this.config.csrfToken);
            
            fetch(element.getAttribute('data-auto-save'), {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.showToast('Auto-saved', 'success');
                }
            })
            .catch(error => {
                console.error('Auto-save error:', error);
            });
        }, 1000);
    },

    // Confirm delete action
    confirmDelete(element) {
        const itemName = element.getAttribute('data-item-name') || 'this item';
        
        Swal.fire({
            title: 'Are you sure?',
            text: `You won't be able to revert this! This will permanently delete ${itemName}.`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#ef4444',
            cancelButtonColor: '#6b7280',
            confirmButtonText: 'Yes, delete it!',
            cancelButtonText: 'Cancel'
        }).then((result) => {
            if (result.isConfirmed) {
                // Proceed with deletion
                const url = element.getAttribute('href') || element.getAttribute('data-url');
                if (url) {
                    window.location.href = url;
                }
            }
        });
    },

    // Toast notification
    showToast(message, type = 'info', duration = 3000) {
        if (typeof Toastify !== 'undefined') {
            const colors = {
                success: '#10b981',
                error: '#ef4444',
                warning: '#f59e0b',
                info: '#3b82f6'
            };

            Toastify({
                text: message,
                duration: duration,
                gravity: "top",
                position: "right",
                backgroundColor: colors[type] || colors.info,
                stopOnFocus: true,
                className: 'toast-notification'
            }).showToast();
        }
    },

    // Loading state
    showLoading(element) {
        element.disabled = true;
        element.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Loading...';
    },

    hideLoading(element, originalText) {
        element.disabled = false;
        element.innerHTML = originalText;
    },

    // AJAX helper
    ajax(url, options = {}) {
        const defaults = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        };

        if (this.config.csrfToken) {
            defaults.headers['X-CSRF-TOKEN'] = this.config.csrfToken;
        }

        return fetch(url, { ...defaults, ...options })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            });
    },

    // Form validation
    validateForm(form) {
        let isValid = true;
        const requiredFields = form.querySelectorAll('[required]');
        
        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                this.showFieldError(field, 'This field is required');
                isValid = false;
            } else {
                this.clearFieldError(field);
            }
        });

        return isValid;
    },

    // Show field error
    showFieldError(field, message) {
        field.classList.add('border-red-500');
        
        let errorElement = field.parentNode.querySelector('.field-error');
        if (!errorElement) {
            errorElement = document.createElement('div');
            errorElement.className = 'field-error text-red-500 text-sm mt-1';
            field.parentNode.appendChild(errorElement);
        }
        errorElement.textContent = message;
    },

    // Clear field error
    clearFieldError(field) {
        field.classList.remove('border-red-500');
        const errorElement = field.parentNode.querySelector('.field-error');
        if (errorElement) {
            errorElement.remove();
        }
    },

    // Format currency
    formatCurrency(amount, currency = 'USD') {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: currency
        }).format(amount);
    },

    // Format date
    formatDate(date, format = 'short') {
        return new Intl.DateTimeFormat('en-US', {
            dateStyle: format
        }).format(new Date(date));
    },

    // Debounce function
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    // Local storage helpers
    storage: {
        set(key, value) {
            try {
                localStorage.setItem(`admin_${key}`, JSON.stringify(value));
            } catch (e) {
                console.error('Storage error:', e);
            }
        },

        get(key) {
            try {
                const item = localStorage.getItem(`admin_${key}`);
                return item ? JSON.parse(item) : null;
            } catch (e) {
                console.error('Storage error:', e);
                return null;
            }
        },

        remove(key) {
            try {
                localStorage.removeItem(`admin_${key}`);
            } catch (e) {
                console.error('Storage error:', e);
            }
        }
    }
};

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    Admin.init();
});

// Export for use in other scripts
window.Admin = Admin;
