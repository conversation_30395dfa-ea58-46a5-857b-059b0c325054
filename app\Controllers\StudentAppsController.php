<?php

namespace App\Controllers;

use App\Models\StudentsModel;
use App\Models\StudentSessionModel;
use App\Models\StudentApplyLeaveModel;
use App\Models\StudentAttendanceModel;
use App\Models\StudentBehaviourModel;
use App\Models\StudentDocModel;
use App\Models\StudentFeesModel;
use App\Models\StudentIncidentsModel;
use App\Models\StudentTimelineModel;
use App\Models\StudentTransportFeesModel;

class StudentAppsController extends BaseController
{
    protected $studentsModel;
    protected $studentSessionModel;
    protected $applyLeaveModel;
    protected $attendanceModel;
    protected $behaviourModel;
    protected $docModel;
    protected $feesModel;
    protected $incidentsModel;
    protected $timelineModel;
    protected $transportFeesModel;

    public function __construct()
    {
        $this->studentsModel = new StudentsModel();
        $this->studentSessionModel = new StudentSessionModel();
        $this->applyLeaveModel = new StudentApplyLeaveModel();
        $this->attendanceModel = new StudentAttendanceModel();
        $this->behaviourModel = new StudentBehaviourModel();
        $this->docModel = new StudentDocModel();
        $this->feesModel = new StudentFeesModel();
        $this->incidentsModel = new StudentIncidentsModel();
        $this->timelineModel = new StudentTimelineModel();
        $this->transportFeesModel = new StudentTransportFeesModel();
    }

    /**
     * Main Student Apps Dashboard
     */
    public function index()
    {
        $data = [
            'title' => 'Student Apps',
            'breadcrumbs' => [
                ['name' => 'Dashboard', 'url' => base_url('admin')],
                ['name' => 'Student Apps', 'url' => '']
            ]
        ];

        return view('admin/student_apps/index', $data);
    }

    /**
     * Student Apply Leave Management
     */
    public function applyLeave()
    {
        return redirect()->to(base_url('admin/student-apps/apply-leave'));
    }

    /**
     * Student Attendance Management
     */
    public function attendance()
    {
        return redirect()->to(base_url('admin/student-apps/attendance'));
    }

    /**
     * Student Behaviour Management
     */
    public function behaviour()
    {
        return redirect()->to(base_url('admin/student-apps/behaviour'));
    }

    /**
     * Student Documents Management
     */
    public function documents()
    {
        return redirect()->to(base_url('admin/student-apps/documents'));
    }

    /**
     * Student Edit Fields Management
     */
    public function editFields()
    {
        return redirect()->to(base_url('admin/student-apps/edit-fields'));
    }

    /**
     * Student Fees Management
     */
    public function fees()
    {
        return redirect()->to(base_url('admin/student-apps/fees'));
    }

    /**
     * Student Fees Deposite Management
     */
    public function feesDeposite()
    {
        return redirect()->to(base_url('admin/student-apps/fees-deposite'));
    }

    /**
     * Student Fees Discounts Management
     */
    public function feesDiscounts()
    {
        return redirect()->to(base_url('admin/student-apps/fees-discounts'));
    }

    /**
     * Student Fees Master Management
     */
    public function feesMaster()
    {
        return redirect()->to(base_url('admin/student-apps/fees-master'));
    }

    /**
     * Student Fees Processing Management
     */
    public function feesProcessing()
    {
        return redirect()->to(base_url('admin/student-apps/fees-processing'));
    }

    /**
     * Student Incident Comments Management
     */
    public function incidentComments()
    {
        return redirect()->to(base_url('admin/student-apps/incident-comments'));
    }

    /**
     * Student Incidents Management
     */
    public function incidents()
    {
        return redirect()->to(base_url('admin/student-apps/incidents'));
    }

    /**
     * Student Session Management
     */
    public function sessions()
    {
        return redirect()->to(base_url('admin/student-apps/sessions'));
    }

    /**
     * Student Subject Attendances Management
     */
    public function subjectAttendances()
    {
        return redirect()->to(base_url('admin/student-apps/subject-attendances'));
    }

    /**
     * Student Timeline Management
     */
    public function timeline()
    {
        return redirect()->to(base_url('admin/student-apps/timeline'));
    }

    /**
     * Student Transport Fees Management
     */
    public function transportFees()
    {
        return redirect()->to(base_url('admin/student-apps/transport-fees'));
    }

    /**
     * Get student apps statistics
     */
    public function getStats()
    {
        try {
            // Get total students
            $totalStudents = $this->studentsModel->where('is_active', 'yes')->countAllResults();

            // Get active sessions
            $activeSessions = $this->studentSessionModel->where('is_active', 'yes')->countAllResults();

            // Calculate total applications from various modules
            $totalApplications = 0;
            $pendingApprovals = 0;

            // Leave applications
            $leaveApplications = $this->applyLeaveModel->countAllResults();
            $pendingLeave = $this->applyLeaveModel->where('status', 'pending')->countAllResults();
            $totalApplications += $leaveApplications;
            $pendingApprovals += $pendingLeave;

            // Documents
            $totalDocuments = $this->docModel->countAllResults();
            $totalApplications += $totalDocuments;

            // Incidents
            $totalIncidents = $this->incidentsModel->countAllResults();
            $totalApplications += $totalIncidents;

            // Timeline entries
            $totalTimeline = $this->timelineModel->countAllResults();
            $totalApplications += $totalTimeline;

            $stats = [
                'total_students' => $totalStudents,
                'active_sessions' => $activeSessions,
                'total_applications' => $totalApplications,
                'pending_approvals' => $pendingApprovals
            ];

            return $this->response->setJSON([
                'success' => true,
                'data' => $stats
            ]);
        } catch (\Exception) {
            // Fallback to basic stats if models don't exist
            $stats = [
                'total_students' => $this->studentsModel->where('is_active', 'yes')->countAllResults(),
                'active_sessions' => $this->studentSessionModel->where('is_active', 'yes')->countAllResults(),
                'total_applications' => 0,
                'pending_approvals' => 0
            ];

            return $this->response->setJSON([
                'success' => true,
                'data' => $stats
            ]);
        }
    }
}
