# 🔐 Student Management System - Authentication Guide

## Overview

This Student Management System implements a comprehensive RBAC (Role-Based Access Control) authentication system using **CodeIgniter Shield** with modern UI/UX design, Google OAuth integration, email notifications, and advanced security features.

## ✅ Features Implemented

### 🔑 Core Authentication Features
- ✅ **User Registration** with role selection
- ✅ **User Login** with remember me functionality
- ✅ **Password Reset** via magic links
- ✅ **Email Activation** for new accounts
- ✅ **Google OAuth** integration
- ✅ **Session Management** with security
- ✅ **Logout** functionality

### 👥 Role-Based Access Control (RBAC)
- ✅ **7 User Roles** with specific permissions
- ✅ **Granular Permissions** for different modules
- ✅ **Permission Matrix** for role-based access
- ✅ **Default Role Assignment** for new users

### 🎨 Modern UI/UX Design
- ✅ **Responsive Design** with Tailwind CSS
- ✅ **Modern Authentication Forms** with animations
- ✅ **Professional Email Templates** with branding
- ✅ **Interactive Elements** with hover effects
- ✅ **Toast Notifications** with Sweet<PERSON>lert2

### 🔔 Notification System
- ✅ **Real-time Notifications** for auth events
- ✅ **Email Notifications** for important actions
- ✅ **Notification Management** with read/unread status
- ✅ **Notification Types** (info, success, warning, error, auth)

## 🏗️ System Architecture

### Database Tables
```
users                    - Main user accounts (Shield)
auth_identities         - User credentials (Shield)
auth_logins             - Login attempts tracking (Shield)
auth_token_logins       - API token logins (Shield)
auth_remember_tokens    - Remember me tokens (Shield)
auth_groups_users       - User role assignments (Shield)
auth_permissions_users  - User-specific permissions (Shield)
notifications           - Custom notification system
```

### User Roles & Permissions

#### 🔴 **Super Admin**
- **Description**: Complete control of the school management system
- **Permissions**: All permissions (`admin.*`, `students.*`, `student-apps.*`, etc.)
- **Access**: Full system access

#### 🟠 **School Admin**
- **Description**: School administrators with full access to student management
- **Permissions**: 
  - `admin.access`, `admin.settings`, `admin.users`, `admin.reports`
  - `students.*`, `student-apps.*`, `attendance.*`, `grades.*`
  - `reports.*`, `communication.*`, `own.*`
- **Access**: Administrative dashboard and all modules

#### 🟡 **Principal**
- **Description**: School principal with oversight access
- **Permissions**:
  - `admin.access`, `admin.reports`
  - `students.view`, `students.edit`
  - `student-apps.view`, `student-apps.approve`
  - `attendance.view`, `grades.view`, `grades.publish`
  - `reports.*`, `communication.*`, `own.*`
- **Access**: Dashboard, reports, and approval workflows

#### 🟢 **Teacher** (Default Role)
- **Description**: Teachers with access to their assigned students and classes
- **Permissions**:
  - `admin.access`
  - `students.view`, `students.edit`
  - `student-apps.view`, `student-apps.create`, `student-apps.edit`
  - `attendance.*`, `grades.*`
  - `reports.view`, `reports.generate`
  - `communication.*`, `own.*`
- **Access**: Student management, attendance, grades, basic reports

#### 🔵 **Staff**
- **Description**: School staff with limited access to student information
- **Permissions**:
  - `admin.access`
  - `students.view`
  - `student-apps.view`, `student-apps.create`
  - `attendance.view`, `reports.view`
  - `communication.view`, `own.*`
- **Access**: Limited student information and basic functions

#### 🟣 **Parent**
- **Description**: Parents with access to their children's information only
- **Permissions**:
  - `children.view`, `communication.view`, `own.*`
- **Access**: Children's information and communication

#### ⚫ **Student**
- **Description**: Students with access to their own information
- **Permissions**:
  - `own.*`, `communication.view`
- **Access**: Personal information and communication

## 🚀 Getting Started

### 1. Authentication URLs

```
Login:           http://studentwablas.me/auth/login
Register:        http://studentwablas.me/auth/register
Password Reset:  http://studentwablas.me/auth/magic-link
Google OAuth:    http://studentwablas.me/auth/google
Admin Dashboard: http://studentwablas.me/admin
```

### 2. Google OAuth Setup

To enable Google OAuth authentication:

1. **Go to Google Cloud Console**: https://console.cloud.google.com/
2. **Create/Select Project**: Create new or select existing project
3. **Enable APIs**: Enable Google+ API and Google OAuth2 API
4. **Create Credentials**: 
   - Go to "Credentials" → "Create Credentials" → "OAuth 2.0 Client IDs"
   - Application type: Web application
   - Authorized redirect URIs: `http://studentwablas.me/auth/google/callback`
5. **Update .env file**:
   ```env
   GOOGLE_CLIENT_ID = 'your-google-client-id'
   GOOGLE_CLIENT_SECRET = 'your-google-client-secret'
   ```

### 3. Email Configuration

Configure email settings in `.env` for notifications:

```env
email.fromEmail = '<EMAIL>'
email.fromName = 'Student Management System'
email.SMTPHost = 'your-smtp-host'
email.SMTPUser = 'your-smtp-username'
email.SMTPPass = 'your-smtp-password'
email.SMTPPort = 587
email.SMTPCrypto = 'tls'
```

## 🔧 Configuration Files

### Key Configuration Files
- `app/Config/Auth.php` - Main authentication configuration
- `app/Config/AuthGroups.php` - RBAC roles and permissions
- `app/Config/OAuth.php` - Google OAuth configuration
- `app/Controllers/AuthController.php` - Custom registration controller
- `app/Controllers/GoogleAuthController.php` - Google OAuth controller

### Authentication Views
- `app/Views/auth/layout.php` - Modern authentication layout
- `app/Views/auth/login.php` - Login form with Google OAuth
- `app/Views/auth/register.php` - Registration form with role selection
- `app/Views/auth/email_activate_show.php` - Email activation page
- `app/Views/auth/magic_link_form.php` - Password reset form
- `app/Views/auth/magic_link_message.php` - Password reset confirmation

### Email Templates
- `app/Views/auth/email/email_activate_email.php` - Account activation email
- `app/Views/auth/email/magic_link_email.php` - Password reset email

## 🛡️ Security Features

### Password Security
- **Minimum 8 characters** required
- **Strong password validation** with composition rules
- **Password similarity checking** against personal information
- **Secure password hashing** using PHP's password_hash()

### Session Security
- **Session regeneration** on login
- **Remember me** functionality with secure tokens
- **Session timeout** configuration
- **CSRF protection** enabled

### Login Security
- **Login attempt tracking** and rate limiting
- **Account lockout** after failed attempts
- **IP address logging** for security auditing
- **Magic link** password reset (expires in 1 hour)

### Email Security
- **Email activation** required for new accounts
- **Secure email templates** with anti-phishing measures
- **Email verification** for password resets
- **Professional branding** to prevent spoofing

## 📱 Modern UI/UX Features

### Design Elements
- **Gradient backgrounds** with floating animations
- **Glass morphism effects** for modern look
- **Responsive design** for all devices
- **Interactive buttons** with hover effects
- **Loading animations** and transitions

### User Experience
- **Auto-focus** on form fields
- **Password visibility toggle**
- **Real-time form validation**
- **Toast notifications** for feedback
- **Smooth page transitions**

### Accessibility
- **ARIA labels** for screen readers
- **Keyboard navigation** support
- **High contrast** color schemes
- **Responsive typography**

## 🧪 Testing

### Run Authentication Tests
```bash
php spark test:auth-system
```

### Test Coverage
- ✅ Database table creation
- ✅ Authentication configuration
- ✅ RBAC setup and permissions
- ✅ View file existence
- ✅ OAuth configuration
- ✅ Notification system
- ✅ User creation and role assignment

## 🔄 Workflow Examples

### New User Registration
1. User visits `/auth/register`
2. Fills form with email, password, and role
3. System creates account with selected role
4. Email activation sent (if enabled)
5. User clicks activation link
6. Account activated and redirected to dashboard

### Google OAuth Login
1. User clicks "Continue with Google"
2. Redirected to Google OAuth consent
3. User authorizes application
4. Google returns user data
5. System creates/updates user account
6. User logged in and redirected to dashboard

### Password Reset
1. User visits `/auth/magic-link`
2. Enters email address
3. Magic link sent to email (expires in 1 hour)
4. User clicks link in email
5. Redirected to login with auto-authentication
6. Password can be changed in profile

## 🎯 Next Steps

### Recommended Enhancements
1. **Two-Factor Authentication (2FA)** - Add SMS/TOTP support
2. **Social Login Expansion** - Add Facebook, Microsoft, etc.
3. **Advanced Audit Logging** - Track all user actions
4. **Password Policy Enforcement** - Configurable password rules
5. **Account Lockout Management** - Admin unlock functionality

### Integration Points
- **Student Information System** - Link auth to student records
- **Learning Management System** - SSO integration
- **Communication Platform** - Unified messaging
- **Reporting System** - Role-based report access

## 📞 Support

For authentication-related issues:
1. Check the authentication test results
2. Verify database migrations are complete
3. Ensure .env configuration is correct
4. Review server logs for errors
5. Contact system administrator

---

**🎓 Student Management System Authentication**  
*Secure, Modern, and User-Friendly*
