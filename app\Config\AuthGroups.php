<?php

declare(strict_types=1);

/**
 * This file is part of CodeIgniter Shield.
 *
 * (c) CodeIgniter Foundation <<EMAIL>>
 *
 * For the full copyright and license information, please view
 * the LICENSE file that was distributed with this source code.
 */

namespace Config;

use CodeIgniter\Shield\Config\AuthGroups as ShieldAuthGroups;

class AuthGroups extends ShieldAuthGroups
{
    /**
     * --------------------------------------------------------------------
     * Default Group
     * --------------------------------------------------------------------
     * The group that a newly registered user is added to.
     */
    public string $defaultGroup = 'teacher';

    /**
     * --------------------------------------------------------------------
     * Groups
     * --------------------------------------------------------------------
     * An associative array of the available groups in the system, where the keys
     * are the group names and the values are arrays of the group info.
     *
     * Whatever value you assign as the key will be used to refer to the group
     * when using functions such as:
     *      $user->addGroup('superadmin');
     *
     * @var array<string, array<string, string>>
     *
     * @see https://codeigniter4.github.io/shield/quick_start_guide/using_authorization/#change-available-groups for more info
     */
    public array $groups = [
        'superadmin' => [
            'title'       => 'Super Admin',
            'description' => 'Complete control of the school management system.',
        ],
        'admin' => [
            'title'       => 'School Admin',
            'description' => 'School administrators with full access to student management.',
        ],
        'principal' => [
            'title'       => 'Principal',
            'description' => 'School principal with oversight access.',
        ],
        'teacher' => [
            'title'       => 'Teacher',
            'description' => 'Teachers with access to their assigned students and classes.',
        ],
        'staff' => [
            'title'       => 'Staff',
            'description' => 'School staff with limited access to student information.',
        ],
        'parent' => [
            'title'       => 'Parent',
            'description' => 'Parents with access to their children\'s information only.',
        ],
        'student' => [
            'title'       => 'Student',
            'description' => 'Students with access to their own information.',
        ],
    ];

    /**
     * --------------------------------------------------------------------
     * Permissions
     * --------------------------------------------------------------------
     * The available permissions in the system.
     *
     * If a permission is not listed here it cannot be used.
     */
    public array $permissions = [
        // Admin permissions
        'admin.access'           => 'Can access the admin dashboard',
        'admin.settings'         => 'Can access system settings',
        'admin.users'            => 'Can manage all users',
        'admin.reports'          => 'Can access all reports',

        // Student management permissions
        'students.view'          => 'Can view student information',
        'students.create'        => 'Can create new students',
        'students.edit'          => 'Can edit student information',
        'students.delete'        => 'Can delete students',
        'students.manage'        => 'Full student management access',

        // Student Apps permissions
        'student-apps.view'      => 'Can view student applications',
        'student-apps.create'    => 'Can create student applications',
        'student-apps.edit'      => 'Can edit student applications',
        'student-apps.delete'    => 'Can delete student applications',
        'student-apps.approve'   => 'Can approve/reject applications',

        // Attendance permissions
        'attendance.view'        => 'Can view attendance records',
        'attendance.mark'        => 'Can mark attendance',
        'attendance.edit'        => 'Can edit attendance records',

        // Grades permissions
        'grades.view'            => 'Can view grades',
        'grades.edit'            => 'Can edit grades',
        'grades.publish'         => 'Can publish grades',

        // Reports permissions
        'reports.view'           => 'Can view reports',
        'reports.generate'       => 'Can generate reports',
        'reports.export'         => 'Can export reports',

        // Communication permissions
        'communication.send'     => 'Can send messages/notifications',
        'communication.view'     => 'Can view messages',

        // Own data permissions
        'own.view'               => 'Can view own information',
        'own.edit'               => 'Can edit own information',
        'children.view'          => 'Can view children information',
    ];

    /**
     * --------------------------------------------------------------------
     * Permissions Matrix
     * --------------------------------------------------------------------
     * Maps permissions to groups.
     *
     * This defines group-level permissions.
     */
    public array $matrix = [
        'superadmin' => [
            'admin.*',
            'students.*',
            'student-apps.*',
            'attendance.*',
            'grades.*',
            'reports.*',
            'communication.*',
            'own.*',
            'children.*',
        ],
        'admin' => [
            'admin.access',
            'admin.settings',
            'admin.users',
            'admin.reports',
            'students.*',
            'student-apps.*',
            'attendance.*',
            'grades.*',
            'reports.*',
            'communication.*',
            'own.*',
        ],
        'principal' => [
            'admin.access',
            'admin.reports',
            'students.view',
            'students.edit',
            'student-apps.view',
            'student-apps.approve',
            'attendance.view',
            'grades.view',
            'grades.publish',
            'reports.*',
            'communication.*',
            'own.*',
        ],
        'teacher' => [
            'admin.access',
            'students.view',
            'students.edit',
            'student-apps.view',
            'student-apps.create',
            'student-apps.edit',
            'attendance.*',
            'grades.*',
            'reports.view',
            'reports.generate',
            'communication.*',
            'own.*',
        ],
        'staff' => [
            'admin.access',
            'students.view',
            'student-apps.view',
            'student-apps.create',
            'attendance.view',
            'reports.view',
            'communication.view',
            'own.*',
        ],
        'parent' => [
            'children.view',
            'communication.view',
            'own.*',
        ],
        'student' => [
            'own.*',
            'communication.view',
        ],
    ];
}
