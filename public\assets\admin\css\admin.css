/* TailAdmin Dashboard Styles */

/* Base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Smooth transitions for all interactive elements */
* {
    transition: all 0.2s ease-in-out;
}

/* TailAdmin Color Variables */
:root {
    --color-primary: #3c50e0;
    --color-secondary: #80caee;
    --color-success: #219653;
    --color-warning: #ffa70b;
    --color-danger: #d34053;
    --color-info: #8155ba;
    --color-dark: #1c2434;
    --color-body: #64748b;
    --color-bodydark: #aeb7c0;
    --color-bodydark1: #dee4ee;
    --color-bodydark2: #8a99af;
    --color-stroke: #e2e8f0;
    --color-strokedark: #2e3a47;
    --color-whiten: #f1f5f9;
    --color-whiter: #f5f7fd;
    --color-boxdark: #24303f;
    --color-boxdark-2: #1a222c;
    --color-meta-1: #dc3545;
    --color-meta-2: #eff2f7;
    --color-meta-3: #10b981;
    --color-meta-4: #313d4a;
    --color-meta-5: #259ae6;
    --color-meta-6: #ffba00;
    --color-meta-7: #ff6766;
    --color-meta-8: #f0950c;
    --color-meta-9: #e5e7eb;
}

/* Width utilities */
.w-72\.5 { width: 18.125rem; }
.w-11\.5 { width: 2.875rem; }
.h-11\.5 { height: 2.875rem; }
.w-8\.5 { width: 2.125rem; }
.h-8\.5 { height: 2.125rem; }
.w-5\.5 { width: 1.375rem; }
.h-5\.5 { height: 1.375rem; }
.w-47\.5 { width: 11.875rem; }
.min-w-47\.5 { min-width: 11.875rem; }
.w-45 { width: 11.25rem; }
.max-w-45 { max-width: 11.25rem; }
.max-w-4 { max-width: 1rem; }
.max-w-2\.5 { max-width: 0.625rem; }
.h-2\.5 { height: 0.625rem; }

/* Padding utilities */
.py-5\.5 { padding-top: 1.375rem; padding-bottom: 1.375rem; }
.py-6\.5 { padding-top: 1.625rem; padding-bottom: 1.625rem; }
.px-7\.5 { padding-left: 1.875rem; padding-right: 1.875rem; }
.pt-7\.5 { padding-top: 1.875rem; }
.pb-5 { padding-bottom: 1.25rem; }

/* Gap utilities */
.gap-7\.5 { gap: 1.875rem; }
.gap-2\.5 { gap: 0.625rem; }

/* Z-index utilities */
.z-999 { z-index: 999; }
.z-99999 { z-index: 99999; }

/* Text size utilities */
.text-title-md { font-size: 1.25rem; line-height: 1.6; }
.text-title-md2 { font-size: 1.5rem; line-height: 1.4; }

/* Color utilities */
.text-black { color: #1c2434; }
.text-dark { color: #1c2434; }
.text-primary { color: var(--color-primary); }
.text-meta-1 { color: var(--color-meta-1); }
.text-meta-3 { color: var(--color-meta-3); }
.text-meta-5 { color: var(--color-meta-5); }
.bg-primary { background-color: var(--color-primary); }
.bg-meta-1 { background-color: var(--color-meta-1); }
.bg-meta-2 { background-color: var(--color-meta-2); }
.bg-meta-4 { background-color: var(--color-meta-4); }
.bg-boxdark { background-color: var(--color-boxdark); }
.bg-whiter { background-color: var(--color-whiter); }
.bg-gray { background-color: #eff4fb; }
.bg-gray-2 { background-color: #f7f9fc; }
.bg-success { background-color: var(--color-success); }
.bg-danger { background-color: var(--color-danger); }
.bg-opacity-10 { background-opacity: 0.1; }
.text-success { color: var(--color-success); }
.text-danger { color: var(--color-danger); }
.text-body { color: var(--color-body); }
.border-stroke { border-color: var(--color-stroke); }
.border-strokedark { border-color: var(--color-strokedark); }

/* Shadow utilities */
.shadow-default { box-shadow: 0px 8px 13px -3px rgba(0, 0, 0, 0.07); }
.shadow-card { box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.08); }
.shadow-1 { box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.05); }
.shadow-2 { box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.08); }
.drop-shadow-1 { filter: drop-shadow(0px 1px 0px #e2e8f0); }

/* Dark mode styles */
.dark {
    color-scheme: dark;
}

.dark .text-black { color: #ffffff; }
.dark .text-dark { color: #ffffff; }
.dark .bg-white { background-color: var(--color-boxdark); }
.dark .border-stroke { border-color: var(--color-strokedark); }

/* No scrollbar utility */
.no-scrollbar::-webkit-scrollbar {
    display: none;
}

.no-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f5f9;
}

.dark ::-webkit-scrollbar-track {
    background: var(--color-boxdark);
}

::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

.dark ::-webkit-scrollbar-thumb {
    background: var(--color-strokedark);
}

::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

.dark ::-webkit-scrollbar-thumb:hover {
    background: #475569;
}

/* Loading animation */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Sidebar styles */
.sidebar-dark {
background: linear-gradient(180deg, #1c2434 0%, #24303f 100%);
height: 100vh; /* Changed to 100vh */
display: flex;
flex-direction: column;
transition: width 0.3s ease-in-out; /* Added transition */
}

.sidebar-dark::-webkit-scrollbar {
width: 6px;
height: 6px;
}

.sidebar-dark::-webkit-scrollbar-track {
background: #f1f5f9;
}

.sidebar-dark::-webkit-scrollbar-thumb {
background: #cbd5e1;
border-radius: 3px;
}

.sidebar-dark::-webkit-scrollbar-thumb:hover {
background: #94a3b8;
}

/* Menu item styles */
.menu-item {
    position: relative;
    transition: all 0.3s ease;
    width: 100%;
}

.menu-item:hover {
    background-color: rgba(255, 255, 255, 0.05);
    color: #ffffff;
}

.menu-item.active {
    background-color: var(--color-primary);
    color: #ffffff;
}

.menu-item.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 3px;
    background-color: #ffffff;
}

/* Enhanced sidebar styles for better responsiveness */
@media (max-width: 1023px) {
    .sidebar-dark {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        max-width: 18rem;
        z-index: 50;
        box-shadow: 4px 0 10px rgba(0, 0, 0, 0.1);
    }
    
    #sidebar-overlay {
        transition: opacity 0.3s ease;
    }
}

/* Enhanced sidebar styles */
.sidebar-transition {
    transition: width 0.3s ease-in-out, transform 0.3s ease-in-out;
}

/* Submenu styles */
.submenu {
    overflow: hidden;
    max-height: 0;
    transition: max-height 0.3s ease-in-out;
    opacity: 0;
}

.submenu.open {
    max-height: 1000px;
    opacity: 1;
}

.menu-arrow {
    transition: transform 0.3s ease;
}

.menu-arrow.rotate {
    transform: rotate(90deg);
}

.menu-item-parent {
    cursor: pointer;
    user-select: none;
}

/* Sidebar minimized state */
.sidebar-minimized {
    width: 5rem !important;
}

.sidebar-minimized .sidebar-logo-text,
.sidebar-minimized .menu-text,
.sidebar-minimized .menu-arrow,
.sidebar-minimized .menu-group-label {
    display: none !important;
}

.sidebar-minimized .submenu {
    display: none !important;
}

/* Hover effect for minimized sidebar on desktop */
@media (min-width: 1024px) {
    .sidebar-minimized:hover {
        width: 18rem !important;
    }
    
    .sidebar-minimized:hover .sidebar-logo-text,
    .sidebar-minimized:hover .menu-text,
    .sidebar-minimized:hover .menu-arrow,
    .sidebar-minimized:hover .menu-group-label {
        display: block !important;
    }
    
    .sidebar-minimized:hover .submenu.open {
        display: block !important;
    }
    
    /* Ensure content area adjusts when sidebar is minimized */
    .sidebar-minimized + .content-area {
        margin-left: 5rem;
    }
    
    .sidebar-minimized:hover + .content-area {
        margin-left: 18rem;
    }
}

/* Ensure proper submenu display on mobile */
@media (max-width: 1023px) {
    .submenu {
        background-color: rgba(0, 0, 0, 0.2);
    }
    
    .submenu.open {
        border-left: 2px solid rgba(255, 255, 255, 0.1);
    }
}

/* Card hover effects */
.card-hover {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.card-hover:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Button animations */
.btn-animate {
    position: relative;
    overflow: hidden;
}

.btn-animate::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-animate:hover::before {
    left: 100%;
}

/* Pulse animation for notifications */
.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Fade in animation */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Slide in from left */
.slide-in-left {
    animation: slideInLeft 0.3s ease-out;
}

@keyframes slideInLeft {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* Custom DataTables styling */
.dataTables_wrapper {
    font-family: 'Inter', sans-serif;
}

.dataTables_filter input {
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    padding: 0.5rem 0.75rem;
    margin-left: 0.5rem;
}

.dataTables_length select {
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    padding: 0.25rem 0.5rem;
    margin: 0 0.5rem;
}

/* Custom form styling */
.form-group {
    margin-bottom: 1rem;
}

.form-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.5rem;
}

.form-input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Status badges */
.status-active {
    background-color: #dcfce7;
    color: #166534;
}

.status-inactive {
    background-color: #fee2e2;
    color: #991b1b;
}

.status-pending {
    background-color: #fef3c7;
    color: #92400e;
}

/* Chart containers */
.chart-container {
    position: relative;
    height: 300px;
    width: 100%;
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
    .mobile-hidden {
        display: none;
    }
    
    .mobile-full {
        width: 100%;
    }
    
    .mobile-stack {
        flex-direction: column;
    }
    
    .mobile-text-center {
        text-align: center;
    }
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    .print-full-width {
        width: 100% !important;
    }
    
    body {
        font-size: 12pt;
        line-height: 1.4;
    }
    
    .page-break {
        page-break-before: always;
    }
}

/* Dark mode support (if needed in future) */
@media (prefers-color-scheme: dark) {
    .dark-mode {
        background-color: #1f2937;
        color: #f9fafb;
    }
    
    .dark-mode .bg-white {
        background-color: #374151;
    }
    
    .dark-mode .text-gray-900 {
        color: #f9fafb;
    }
    
    .dark-mode .border-gray-200 {
        border-color: #4b5563;
    }
}

/* Custom utility classes */
.text-shadow {
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.backdrop-blur {
    backdrop-filter: blur(10px);
}

.gradient-bg {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.glass-effect {
    background: rgba(255, 255, 255, 0.25);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.18);
}

/* Top navigation dropdown styles */
#notification-dropdown,
#quick-actions-dropdown,
#user-dropdown {
    transform-origin: top right;
    transition: all 0.2s ease-in-out;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    z-index: 1000;
}

#notification-dropdown.hidden,
#quick-actions-dropdown.hidden,
#user-dropdown.hidden {
    opacity: 0;
    transform: scale(0.95);
    pointer-events: none;
}

#notification-dropdown:not(.hidden),
#quick-actions-dropdown:not(.hidden),
#user-dropdown:not(.hidden) {
    opacity: 1;
    transform: scale(1);
}

/* Notification styles */
.h-12\.5 {
    height: 3.125rem;
}

.w-12\.5 {
    width: 3.125rem;
}

.px-4\.5 {
    padding-left: 1.125rem;
    padding-right: 1.125rem;
}

.bg-primary-100 {
    background-color: rgba(59, 130, 246, 0.1);
}

.bg-warning-100 {
    background-color: rgba(245, 158, 11, 0.1);
}

.text-warning {
    color: #f59e0b;
}

/* Responsive dropdown positioning */
@media (max-width: 640px) {
    #notification-dropdown,
    #quick-actions-dropdown {
        width: 18rem;
        right: -5rem;
    }
    
    #user-dropdown {
        width: 15rem;
        right: 0;
    }
}

/* Loading spinner */
.spinner {
    border: 2px solid #f3f4f6;
    border-top: 2px solid #3b82f6;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Success/Error states */
.success-state {
    border-left: 4px solid #10b981;
    background-color: #ecfdf5;
    padding: 1rem;
    border-radius: 0.375rem;
}

.error-state {
    border-left: 4px solid #ef4444;
    background-color: #fef2f2;
    padding: 1rem;
    border-radius: 0.375rem;
}

.warning-state {
    border-left: 4px solid #f59e0b;
    background-color: #fffbeb;
    padding: 1rem;
    border-radius: 0.375rem;
}

/* Custom checkbox and radio styles */
.custom-checkbox {
    appearance: none;
    width: 1rem;
    height: 1rem;
    border: 2px solid #d1d5db;
    border-radius: 0.25rem;
    background-color: white;
    position: relative;
    cursor: pointer;
}

.custom-checkbox:checked {
    background-color: #3b82f6;
    border-color: #3b82f6;
}

.custom-checkbox:checked::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 0.75rem;
    font-weight: bold;
}

/* Tooltip styles */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltiptext {
    visibility: hidden;
    width: 120px;
    background-color: #374151;
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 5px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -60px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 0.75rem;
}

.tooltip:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
}

/* Progress bars */
.progress-bar {
    width: 100%;
    height: 0.5rem;
    background-color: #e5e7eb;
    border-radius: 0.25rem;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background-color: #3b82f6;
    transition: width 0.3s ease-in-out;
}

/* Floating action button */
.fab {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 3.5rem;
    height: 3.5rem;
    background-color: #3b82f6;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    cursor: pointer;
    z-index: 1000;
}

.fab:hover {
    background-color: #2563eb;
    transform: scale(1.1);
}

/* Breadcrumb styling */
.breadcrumb {
    display: flex;
    align-items: center;
    font-size: 0.875rem;
    color: #6b7280;
}

.breadcrumb-item {
    display: flex;
    align-items: center;
}

.breadcrumb-item:not(:last-child)::after {
    content: '/';
    margin: 0 0.5rem;
    color: #d1d5db;
}

.breadcrumb-item.active {
    color: #374151;
    font-weight: 500;
}

/* Custom scrollable area */
.scrollable {
    max-height: 400px;
    overflow-y: auto;
    padding-right: 0.5rem;
}

/* Highlight animation */
.highlight {
    animation: highlight 2s ease-in-out;
}

@keyframes highlight {
    0% { background-color: transparent; }
    50% { background-color: #fef3c7; }
    100% { background-color: transparent; }
}
